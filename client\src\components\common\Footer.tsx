import Link from 'next/link'

export function Footer() {
  return (
    <footer className="bg-gray-900 py-12 text-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="col-span-1 md:col-span-2">
            <h3 className="mb-4 text-xl font-bold">Ultimate Electrical Designer</h3>
            <p className="mb-4 text-gray-400">
              Professional electrical design software for industrial applications, heat tracing
              systems, and comprehensive project management.
            </p>
          </div>

          <div>
            <h4 className="mb-4 text-lg font-semibold">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/dashboard"
                  className="text-gray-400 transition-colors hover:text-white"
                >
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/login" className="text-gray-400 transition-colors hover:text-white">
                  Login
                </Link>
              </li>
              <li>
                <Link href="/docs" className="text-gray-400 transition-colors hover:text-white">
                  Documentation
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 text-lg font-semibold">Support</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="text-gray-400 transition-colors hover:text-white">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 transition-colors hover:text-white">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/api" className="text-gray-400 transition-colors hover:text-white">
                  API Docs
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-800 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 Ultimate Electrical Designer. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
