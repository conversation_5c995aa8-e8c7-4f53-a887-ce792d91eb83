/**
 * ComponentFilters Unit Tests
 * Tests the ComponentFilters component with filter controls and state management
 */

import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ComponentFilters } from '../../components/ComponentFilters'
import { renderWithProviders, mockComponentFilters } from '@/test/utils'
import { ComponentCategoryType, ComponentType } from '../../types'

describe('ComponentFilters', () => {
  const mockHandlers = {
    onFiltersChange: vi.fn(),
    onClearFilters: vi.fn(),
    onApplyFilters: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders all filter controls', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/component type/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/manufacturer/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/price range/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/stock status/i)).toBeInTheDocument()
    })

    it('renders with initial filter values', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByDisplayValue('RESISTOR')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Test Electronics')).toBeInTheDocument()
    })

    it('renders clear filters button', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByRole('button', { name: /clear filters/i })).toBeInTheDocument()
    })

    it('renders apply filters button', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByRole('button', { name: /apply filters/i })).toBeInTheDocument()
    })

    it('shows active filter count', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByText(/6 filters active/i)).toBeInTheDocument()
    })
  })

  describe('Category Filter', () => {
    it('calls onFiltersChange when category is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      await user.selectOptions(categorySelect, 'CAPACITOR')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        category: 'CAPACITOR' as ComponentCategoryType,
      })
    })

    it('renders all category options', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      expect(categorySelect).toBeInTheDocument()

      // Check for some key options
      expect(screen.getByRole('option', { name: /resistor/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /capacitor/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /inductor/i })).toBeInTheDocument()
    })
  })

  describe('Component Type Filter', () => {
    it('calls onFiltersChange when component type is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const typeSelect = screen.getByLabelText(/component type/i)
      await user.selectOptions(typeSelect, 'CAPACITOR')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        component_type: 'CAPACITOR' as ComponentType,
      })
    })

    it('updates component type options based on category', async () => {
      const user = userEvent.setup()

      renderWithProviders(
        <ComponentFilters
          filters={{ category: 'RESISTOR' as ComponentCategoryType }}
          {...mockHandlers}
        />
      )

      const typeSelect = screen.getByLabelText(/component type/i)
      expect(typeSelect).toBeInTheDocument()

      // Should show resistor-specific types
      expect(screen.getByRole('option', { name: /fixed resistor/i })).toBeInTheDocument()
    })
  })

  describe('Manufacturer Filter', () => {
    it('calls onFiltersChange when manufacturer is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const manufacturerInput = screen.getByLabelText(/manufacturer/i)
      await user.type(manufacturerInput, 'New Manufacturer')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        manufacturer: 'New Manufacturer',
      })
    })

    it('provides manufacturer suggestions', async () => {
      const user = userEvent.setup()

      renderWithProviders(
        <ComponentFilters
          filters={{}}
          manufacturerSuggestions={['Test Electronics', 'Quality Components']}
          {...mockHandlers}
        />
      )

      const manufacturerInput = screen.getByLabelText(/manufacturer/i)
      await user.click(manufacturerInput)

      expect(screen.getByText('Test Electronics')).toBeInTheDocument()
      expect(screen.getByText('Quality Components')).toBeInTheDocument()
    })
  })

  describe('Price Range Filter', () => {
    it('calls onFiltersChange when min price is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const minPriceInput = screen.getByLabelText(/minimum price/i)
      await user.type(minPriceInput, '0.50')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        min_price: 0.5,
      })
    })

    it('calls onFiltersChange when max price is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const maxPriceInput = screen.getByLabelText(/maximum price/i)
      await user.type(maxPriceInput, '2.00')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        max_price: 2.0,
      })
    })

    it('validates price range', async () => {
      const user = userEvent.setup()

      renderWithProviders(
        <ComponentFilters filters={{ min_price: 1.0, max_price: 0.5 }} {...mockHandlers} />
      )

      expect(
        screen.getByText(/minimum price cannot be greater than maximum price/i)
      ).toBeInTheDocument()
    })

    it('renders currency selector', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      expect(screen.getByLabelText(/currency/i)).toBeInTheDocument()
    })
  })

  describe('Stock Status Filter', () => {
    it('calls onFiltersChange when stock status is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const stockSelect = screen.getByLabelText(/stock status/i)
      await user.selectOptions(stockSelect, 'available')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        stock_status: 'available',
      })
    })

    it('renders all stock status options', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      expect(screen.getByRole('option', { name: /available/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /low stock/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /out of stock/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /discontinued/i })).toBeInTheDocument()
    })
  })

  describe('Boolean Filters', () => {
    it('calls onFiltersChange when preferred filter is toggled', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const preferredCheckbox = screen.getByLabelText(/preferred only/i)
      await user.click(preferredCheckbox)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        is_preferred: true,
      })
    })

    it('calls onFiltersChange when active filter is toggled', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const activeCheckbox = screen.getByLabelText(/active only/i)
      await user.click(activeCheckbox)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        is_active: true,
      })
    })
  })

  describe('Filter Actions', () => {
    it('calls onClearFilters when clear button is clicked', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      const clearButton = screen.getByRole('button', { name: /clear filters/i })
      await user.click(clearButton)

      expect(mockHandlers.onClearFilters).toHaveBeenCalled()
    })

    it('calls onApplyFilters when apply button is clicked', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      const applyButton = screen.getByRole('button', { name: /apply filters/i })
      await user.click(applyButton)

      expect(mockHandlers.onApplyFilters).toHaveBeenCalledWith(mockComponentFilters)
    })

    it('disables apply button when no filters are active', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const applyButton = screen.getByRole('button', { name: /apply filters/i })
      expect(applyButton).toBeDisabled()
    })

    it('disables clear button when no filters are active', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const clearButton = screen.getByRole('button', { name: /clear filters/i })
      expect(clearButton).toBeDisabled()
    })
  })

  describe('Collapsible Sections', () => {
    it('toggles advanced filters section', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const advancedToggle = screen.getByRole('button', { name: /advanced filters/i })
      await user.click(advancedToggle)

      expect(screen.getByText(/specifications/i)).toBeInTheDocument()
    })

    it('remembers collapsed state', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const advancedToggle = screen.getByRole('button', { name: /advanced filters/i })

      // Expand
      await user.click(advancedToggle)
      expect(screen.getByText(/specifications/i)).toBeInTheDocument()

      // Collapse
      await user.click(advancedToggle)
      expect(screen.queryByText(/specifications/i)).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByRole('region', { name: /component filters/i })).toBeInTheDocument()
    })

    it('associates labels with form controls', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      expect(categorySelect).toHaveAttribute('id')

      const label = screen.getByText(/category/i)
      expect(label).toHaveAttribute('for', categorySelect.id)
    })

    it('provides proper focus management', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      await user.tab()

      expect(categorySelect).toHaveFocus()
    })
  })

  describe('Edge Cases', () => {
    it('handles missing handlers gracefully', () => {
      renderWithProviders(<ComponentFilters filters={{}} />)

      expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    })

    it('handles invalid filter values', () => {
      const invalidFilters = {
        min_price: -1,
        max_price: 'invalid' as any,
        category: 'INVALID_CATEGORY' as any,
      }

      renderWithProviders(<ComponentFilters filters={invalidFilters} {...mockHandlers} />)

      expect(screen.getByText(/invalid price range/i)).toBeInTheDocument()
    })

    it('handles very large filter objects', () => {
      const largeFilters = {
        ...mockComponentFilters,
        custom_field_1: 'value1',
        custom_field_2: 'value2',
        // ... many more fields
      }

      renderWithProviders(<ComponentFilters filters={largeFilters} {...mockHandlers} />)

      expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('debounces filter changes', async () => {
      const user = userEvent.setup()
      vi.useFakeTimers()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const manufacturerInput = screen.getByLabelText(/manufacturer/i)
      await user.type(manufacturerInput, 'Test')

      // Should not call immediately
      expect(mockHandlers.onFiltersChange).not.toHaveBeenCalled()

      // Advance timers
      vi.advanceTimersByTime(300)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        manufacturer: 'Test',
      })

      vi.useRealTimers()
    })
  })
})
