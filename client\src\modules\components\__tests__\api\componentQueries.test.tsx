/**
 * Component React Query Hooks Tests
 * Tests the React Query hooks for component data fetching
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import {
  useComponents,
  useComponent,
  useComponentSearch,
  useComponentAdvancedSearch,
  useComponentStats,
  useComponentSummary,
  useComponentSuggestions,
} from '../../api/componentQueries'
import {
  mockComponent,
  mockComponentPaginatedResponse,
  mockComponentStats,
  mockApiSuccess,
  mockApiError,
} from '@/test/utils'

// Mock the API client
vi.mock('../../api/componentApi', () => ({
  componentApi: {
    list: vi.fn(),
    getById: vi.fn(),
    search: vi.fn(),
    advancedSearch: vi.fn(),
    getStats: vi.fn(),
    getSummary: vi.fn(),
    getSuggestions: vi.fn(),
  },
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('Component Query Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('useComponents', () => {
    it('fetches components successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))

      const { result } = renderHook(() => useComponents(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentPaginatedResponse)
      expect(componentApi.list).toHaveBeenCalledWith({})
    })

    it('fetches components with parameters', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))

      const params = {
        page: 2,
        size: 20,
        search_term: 'resistor',
        category: 'RESISTOR',
      }

      const { result } = renderHook(() => useComponents(params), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.list).toHaveBeenCalledWith(params)
    })

    it('handles API errors', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const error = mockApiError('Failed to fetch components')
      vi.mocked(componentApi.list).mockResolvedValue(error)

      const { result } = renderHook(() => useComponents(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe('Failed to fetch components')
    })

    it('uses correct query key', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))

      const params = { page: 1, category: 'RESISTOR' }

      renderHook(() => useComponents(params), { wrapper: createWrapper() })

      // Query key should include parameters for proper caching
      expect(componentApi.list).toHaveBeenCalledWith(params)
    })

    it('respects staleTime configuration', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))

      const { result } = renderHook(() => useComponents({}, { staleTime: 10 * 60 * 1000 }), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Should not refetch immediately due to staleTime
      expect(componentApi.list).toHaveBeenCalledTimes(1)
    })
  })

  describe('useComponent', () => {
    it('fetches single component successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.getById).mockResolvedValue(mockApiSuccess(mockComponent))

      const { result } = renderHook(() => useComponent(1), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponent)
      expect(componentApi.getById).toHaveBeenCalledWith(1)
    })

    it('handles component not found', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const error = mockApiError('Component not found', 'COMPONENT_NOT_FOUND')
      vi.mocked(componentApi.getById).mockResolvedValue(error)

      const { result } = renderHook(() => useComponent(999), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Component not found')
    })

    it('does not fetch when ID is undefined', () => {
      const { componentApi } = await import('../../api/componentApi')

      renderHook(() => useComponent(undefined), { wrapper: createWrapper() })

      expect(componentApi.getById).not.toHaveBeenCalled()
    })

    it('enables/disables query based on ID', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.getById).mockResolvedValue(mockApiSuccess(mockComponent))

      const { result, rerender } = renderHook(({ id }) => useComponent(id), {
        wrapper: createWrapper(),
        initialProps: { id: undefined as number | undefined },
      })

      expect(result.current.isFetching).toBe(false)

      rerender({ id: 1 })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.getById).toHaveBeenCalledWith(1)
    })
  })

  describe('useComponentSearch', () => {
    it('searches components successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.search).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const searchParams = {
        query: 'resistor',
        field: 'name' as const,
      }

      const { result } = renderHook(() => useComponentSearch(searchParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentPaginatedResponse)
      expect(componentApi.search).toHaveBeenCalledWith(searchParams, {})
    })

    it('searches with pagination', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.search).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const searchParams = {
        query: 'resistor',
        field: 'name' as const,
      }
      const pagination = { page: 2, size: 20 }

      const { result } = renderHook(() => useComponentSearch(searchParams, pagination), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.search).toHaveBeenCalledWith(searchParams, pagination)
    })

    it('handles empty search results', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const emptyResponse = { ...mockComponentPaginatedResponse, items: [], total: 0 }
      vi.mocked(componentApi.search).mockResolvedValue(mockApiSuccess(emptyResponse))

      const searchParams = {
        query: 'nonexistent',
        field: 'name' as const,
      }

      const { result } = renderHook(() => useComponentSearch(searchParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.items).toEqual([])
      expect(result.current.data?.total).toBe(0)
    })

    it('does not search with empty query', () => {
      const { componentApi } = await import('../../api/componentApi')

      const searchParams = {
        query: '',
        field: 'name' as const,
      }

      renderHook(() => useComponentSearch(searchParams), { wrapper: createWrapper() })

      expect(componentApi.search).not.toHaveBeenCalled()
    })
  })

  describe('useComponentAdvancedSearch', () => {
    it('performs advanced search successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.advancedSearch).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const searchParams = {
        filters: {
          category: 'RESISTOR' as const,
          manufacturer: 'Test Electronics',
        },
        specifications: {
          resistance: { value: '1000', operator: 'eq' as const },
        },
      }

      const { result } = renderHook(() => useComponentAdvancedSearch(searchParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentPaginatedResponse)
      expect(componentApi.advancedSearch).toHaveBeenCalledWith(searchParams, {})
    })

    it('handles complex filter combinations', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.advancedSearch).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const complexParams = {
        filters: {
          category: 'RESISTOR' as const,
          price_range: { min: 0.1, max: 1.0 },
          is_preferred: true,
        },
        specifications: {
          resistance: { value: '1000', operator: 'gte' as const },
          tolerance: { value: '1%', operator: 'lte' as const },
        },
      }

      const { result } = renderHook(() => useComponentAdvancedSearch(complexParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.advancedSearch).toHaveBeenCalledWith(complexParams, {})
    })
  })

  describe('useComponentStats', () => {
    it('fetches component statistics successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.getStats).mockResolvedValue(mockApiSuccess(mockComponentStats))

      const { result } = renderHook(() => useComponentStats(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentStats)
      expect(componentApi.getStats).toHaveBeenCalled()
    })

    it('handles stats fetch errors', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const error = mockApiError('Failed to fetch stats')
      vi.mocked(componentApi.getStats).mockResolvedValue(error)

      const { result } = renderHook(() => useComponentStats(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Failed to fetch stats')
    })

    it('caches stats data appropriately', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.getStats).mockResolvedValue(mockApiSuccess(mockComponentStats))

      const { result: result1 } = renderHook(() => useComponentStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      const { result: result2 } = renderHook(() => useComponentStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result2.current.isSuccess).toBe(true)
      })

      // Should use cached data
      expect(componentApi.getStats).toHaveBeenCalledTimes(1)
    })
  })

  describe('useComponentSummary', () => {
    it('fetches component summary successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const mockSummary = {
        recent_components: [mockComponent],
        popular_components: [mockComponent],
        low_stock_components: [],
      }
      vi.mocked(componentApi.getSummary).mockResolvedValue(mockApiSuccess(mockSummary))

      const { result } = renderHook(() => useComponentSummary(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockSummary)
      expect(componentApi.getSummary).toHaveBeenCalled()
    })
  })

  describe('useComponentSuggestions', () => {
    it('fetches suggestions successfully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      const mockSuggestions = ['resistor 1k', 'resistor 2k', 'resistor 10k']
      vi.mocked(componentApi.getSuggestions).mockResolvedValue(mockApiSuccess(mockSuggestions))

      const { result } = renderHook(() => useComponentSuggestions('res', 'name'), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockSuggestions)
      expect(componentApi.getSuggestions).toHaveBeenCalledWith('res', 'name')
    })

    it('does not fetch suggestions for empty query', () => {
      const { componentApi } = await import('../../api/componentApi')

      renderHook(() => useComponentSuggestions('', 'name'), { wrapper: createWrapper() })

      expect(componentApi.getSuggestions).not.toHaveBeenCalled()
    })

    it('debounces suggestion requests', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.getSuggestions).mockResolvedValue(mockApiSuccess([]))

      const { rerender } = renderHook(({ query }) => useComponentSuggestions(query, 'name'), {
        wrapper: createWrapper(),
        initialProps: { query: 'r' },
      })

      rerender({ query: 're' })
      rerender({ query: 'res' })

      // Should debounce and only make one request
      await waitFor(() => {
        expect(componentApi.getSuggestions).toHaveBeenCalledTimes(1)
      })

      expect(componentApi.getSuggestions).toHaveBeenCalledWith('res', 'name')
    })
  })

  describe('Query Invalidation and Refetching', () => {
    it('supports manual refetch', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))

      const { result } = renderHook(() => useComponents(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.list).toHaveBeenCalledTimes(1)

      await result.current.refetch()

      expect(componentApi.list).toHaveBeenCalledTimes(2)
    })

    it('handles refetch errors gracefully', async () => {
      const { componentApi } = await import('../../api/componentApi')
      vi.mocked(componentApi.list)
        .mockResolvedValueOnce(mockApiSuccess(mockComponentPaginatedResponse))
        .mockResolvedValueOnce(mockApiError('Refetch failed'))

      const { result } = renderHook(() => useComponents(), { wrapper: createWrapper() })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      await result.current.refetch()

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe('Refetch failed')
    })
  })
})
