'use client'

/**
 * ComponentCard - Molecule component for displaying component summary
 * Follows atomic design principles and displays key component information
 */

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { DollarSign, Edit, Eye, Heart, Package, Ruler, Star, Trash2, Weight } from 'lucide-react'
import React from 'react'
import type { ComponentRead } from '../types'
import {
  formatComponentName,
  formatDimensions,
  formatPrice,
  formatWeight,
  getComponentStatusColor,
  getComponentStatusText,
} from '../utils'

export interface ComponentCardProps {
  component: ComponentRead
  isSelected?: boolean
  showActions?: boolean
  compact?: boolean
  onSelect?: (component: ComponentRead) => void
  onEdit?: (component: ComponentRead) => void
  onDelete?: (component: ComponentRead) => void
  onView?: (component: ComponentRead) => void
  onTogglePreferred?: (component: ComponentRead) => void
}

export function ComponentCard({
  component,
  isSelected = false,
  showActions = true,
  compact = false,
  onSelect,
  onEdit,
  onDelete,
  onView,
  onTogglePreferred,
}: ComponentCardProps) {
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(component)
    } else if (onView) {
      onView(component)
    }
  }

  const statusColor = getComponentStatusColor(component)
  const statusText = getComponentStatusText(component)

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleCardClick()
    }
  }

  return (
    <Card
      role="article"
      tabIndex={0}
      aria-label={`Component: ${formatComponentName(component)}, ${component.manufacturer}`}
      className={`
        cursor-pointer transition-all duration-200 hover:shadow-md
        ${isSelected ? 'bg-blue-50 ring-2 ring-blue-500' : ''}
        ${!component.is_active ? 'opacity-60' : ''}
        ${compact ? 'p-2' : ''}
      `}
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
    >
      <CardHeader className={`pb-2 ${compact ? 'p-2' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="min-w-0 flex-1">
            <h3
              className={`truncate font-semibold text-gray-900 ${compact ? 'text-sm' : 'text-base'}`}
            >
              {formatComponentName(component)}
            </h3>
            <p className={`truncate text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
              {component.manufacturer} • {component.model_number}
            </p>
          </div>

          <div className="ml-2 flex items-center gap-1">
            {component.is_preferred && (
              <>
                <Star className="h-4 w-4 fill-current text-yellow-500" />
                <Badge variant="secondary" className="bg-yellow-100 text-xs text-yellow-800">
                  Preferred
                </Badge>
              </>
            )}
            {!component.is_active && (
              <Badge variant="secondary" className="bg-gray-100 text-xs text-gray-600">
                Inactive
              </Badge>
            )}
            {component.is_active && (
              <Badge
                variant={component.is_active ? 'default' : 'secondary'}
                className={`text-xs ${statusColor}`}
              >
                {statusText}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={`pt-0 ${compact ? 'p-2 pt-0' : ''}`}>
        {!compact && (
          <>
            {/* Component Type and Category */}
            <div className="mb-2 flex flex-wrap gap-1">
              {component.component_type && (
                <Badge variant="outline" className="text-xs">
                  <Package className="mr-1 h-3 w-3" />
                  {component.component_type}
                </Badge>
              )}
              {component.category && (
                <Badge variant="outline" className="text-xs">
                  {component.category}
                </Badge>
              )}
            </div>

            {/* Description */}
            {component.description && (
              <p className="mb-3 line-clamp-2 text-sm text-gray-600">{component.description}</p>
            )}
          </>
        )}

        {/* Key Specifications */}
        <div
          className={`mb-3 grid gap-2 ${compact ? 'grid-cols-2' : 'grid-cols-2 sm:grid-cols-3'}`}
        >
          {component.unit_price && (
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-gray-400" />
              <span className={`font-medium ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatPrice(component.unit_price, component.currency)}
              </span>
            </div>
          )}

          {component.weight_kg && (
            <div className="flex items-center gap-1">
              <Weight className="h-3 w-3 text-gray-400" />
              <span className={`text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatWeight(component.weight_kg)}
              </span>
            </div>
          )}

          {component.dimensions && (
            <div className="flex items-center gap-1">
              <Ruler className="h-3 w-3 text-gray-400" />
              <span className={`text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatDimensions(component.dimensions)}
              </span>
            </div>
          )}
        </div>

        {/* Price Information */}
        {!compact && (
          <div className="mb-3 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">
              Price: {component.price ? formatPrice(component.price, component.currency) : 'N/A'}
            </span>
          </div>
        )}

        {/* Supplier Information */}
        {!compact && (component.supplier || component.part_number) && (
          <div className="mb-3 text-xs text-gray-500">
            {component.supplier && `Supplier: ${component.supplier}`}
            {component.supplier && component.part_number && ' • '}
            {component.part_number && `Part: ${component.part_number}`}
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between border-t pt-2">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onView?.(component)
                }}
                className="h-8 w-8 p-0"
                aria-label={`View ${formatComponentName(component)}`}
              >
                <Eye className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit?.(component)
                }}
                className="h-8 w-8 p-0"
                aria-label={`Edit ${formatComponentName(component)}`}
              >
                <Edit className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onTogglePreferred?.(component)
                }}
                className={`h-8 w-8 p-0 ${component.is_preferred ? 'text-yellow-500' : ''}`}
                aria-label={`Toggle preferred status for ${formatComponentName(component)}`}
              >
                <Heart className={`h-4 w-4 ${component.is_preferred ? 'fill-current' : ''}`} />
              </Button>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onDelete?.(component)
              }}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              aria-label={`Delete ${formatComponentName(component)}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Skeleton component for loading states
export function ComponentCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className={compact ? 'p-2' : ''}>
      <CardHeader className={`pb-2 ${compact ? 'p-2' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div
              className={`animate-pulse rounded bg-gray-200 ${compact ? 'mb-1 h-4' : 'mb-2 h-5'}`}
            />
            <div className={`w-3/4 animate-pulse rounded bg-gray-200 ${compact ? 'h-3' : 'h-4'}`} />
          </div>
          <div className="ml-2 h-6 w-16 animate-pulse rounded bg-gray-200" />
        </div>
      </CardHeader>

      <CardContent className={`pt-0 ${compact ? 'p-2 pt-0' : ''}`}>
        {!compact && (
          <>
            <div className="mb-2 flex gap-2">
              <div className="h-6 w-20 animate-pulse rounded bg-gray-200" />
              <div className="h-6 w-24 animate-pulse rounded bg-gray-200" />
            </div>
            <div className="mb-3 h-10 animate-pulse rounded bg-gray-200" />
          </>
        )}

        <div className={`mb-3 grid gap-2 ${compact ? 'grid-cols-2' : 'grid-cols-3'}`}>
          <div className="h-4 animate-pulse rounded bg-gray-200" />
          <div className="h-4 animate-pulse rounded bg-gray-200" />
          <div className="h-4 animate-pulse rounded bg-gray-200" />
        </div>

        <div className="flex justify-between border-t pt-2">
          <div className="flex gap-1">
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
          </div>
          <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
        </div>
      </CardContent>
    </Card>
  )
}
