/**
 * ComponentForm Component Tests
 * Tests the ComponentForm component including form rendering, validation, submission workflows,
 * accessibility compliance, and integration with useComponentForm hook
 */

import { createMockComponent, mockComponent } from '@/test/factories/componentFactories'
import { renderWithProviders } from '@/test/utils'
import { fireEvent, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComponentForm } from '../../components/ComponentForm'

// Mock the API hooks
vi.mock('../../api/componentQueries', () => ({
  useComponentCategories: vi.fn(() => ({
    data: [
      { id: 1, name: 'Resistor', value: 'RESISTOR' },
      { id: 2, name: 'Capacitor', value: 'CAPACITOR' },
    ],
    isLoading: false,
  })),
  useComponentTypes: vi.fn(() => ({
    data: [
      { id: 1, name: 'Fixed Resistor', value: 'FIXED_RESISTOR' },
      { id: 2, name: 'Variable Resistor', value: 'VARIABLE_RESISTOR' },
    ],
    isLoading: false,
  })),
}))

// Mock the validation utility
vi.mock('../../utils', () => ({
  validateComponent: vi.fn((data, isEditing) => ({
    isValid: true,
    errors: [],
  })),
}))

describe('ComponentForm', () => {
  const mockHandlers = {
    onSubmit: vi.fn(),
    onCancel: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders create form with correct title and fields', () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      expect(screen.getByText('Create New Component')).toBeInTheDocument()
      expect(screen.getByLabelText(/component name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/manufacturer/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/part number/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/model number/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    })

    it('renders edit form with correct title and populated fields', () => {
      renderWithProviders(
        <ComponentForm component={mockComponent} isEditing={true} {...mockHandlers} />
      )

      expect(screen.getByText('Edit Component')).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockComponent.name)).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockComponent.manufacturer)).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockComponent.part_number)).toBeInTheDocument()
    })

    it('renders with loading state', () => {
      renderWithProviders(<ComponentForm isLoading={true} {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      expect(submitButton).toBeDisabled()
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('applies custom className', () => {
      const customClass = 'custom-form-class'
      renderWithProviders(<ComponentForm className={customClass} {...mockHandlers} />)

      const formCard = screen.getByRole('form').closest('.custom-form-class')
      expect(formCard).toBeInTheDocument()
    })
  })

  describe('Form Interactions', () => {
    it('handles text input changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'New Component Name')

      expect(nameInput).toHaveValue('New Component Name')
    })

    it('handles select dropdown changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      await user.selectOptions(categorySelect, '1')

      expect(categorySelect).toHaveValue('1')
    })

    it('handles checkbox changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const preferredCheckbox = screen.getByLabelText(/preferred/i)
      await user.click(preferredCheckbox)

      expect(preferredCheckbox).toBeChecked()
    })

    it('handles number input changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const priceInput = screen.getByLabelText(/unit price/i)
      await user.type(priceInput, '15.99')

      expect(priceInput).toHaveValue(15.99)
    })
  })

  describe('Form Validation', () => {
    it('displays validation errors for required fields', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          { field: 'name', message: 'Component name is required', code: 'REQUIRED' },
          { field: 'manufacturer', message: 'Manufacturer is required', code: 'REQUIRED' },
        ],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Component name is required')).toBeInTheDocument()
        expect(screen.getByText('Manufacturer is required')).toBeInTheDocument()
      })
    })

    it('clears field errors when field is modified', async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import('../../utils')

      // Initially return validation error
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Component name is required', code: 'REQUIRED' }],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Trigger validation error
      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Component name is required')).toBeInTheDocument()
      })

      // Clear error when field is modified
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'Valid Name')

      await waitFor(() => {
        expect(screen.queryByText('Component name is required')).not.toBeInTheDocument()
      })
    })

    it('validates form on submission', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      expect(validateComponent).toHaveBeenCalled()
    })
  })

  describe('Form Submission', () => {
    it('submits form with valid data', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Fill form with valid data
      const nameInput = screen.getByLabelText(/component name/i)
      fireEvent.change(nameInput, { target: { value: 'Test Component' } })

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockHandlers.onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Test Component',
          })
        )
      })
    })

    it('does not submit form with invalid data', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Required', code: 'REQUIRED' }],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      expect(mockHandlers.onSubmit).not.toHaveBeenCalled()
    })

    it('handles form cancellation', () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      fireEvent.click(cancelButton)

      expect(mockHandlers.onCancel).toHaveBeenCalled()
    })

    it('disables submit button when form is not dirty', () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      expect(submitButton).toBeDisabled()
    })

    it('enables submit button when form is dirty', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'Test')

      const submitButton = screen.getByRole('button', { name: /create/i })
      expect(submitButton).not.toBeDisabled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and attributes', () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const form = screen.getByRole('form')
      expect(form).toBeInTheDocument()

      const nameInput = screen.getByLabelText(/component name/i)
      expect(nameInput).toHaveAttribute('aria-required', 'true')

      const submitButton = screen.getByRole('button', { name: /create/i })
      expect(submitButton).toHaveAttribute('type', 'submit')
    })

    it('associates error messages with form fields', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Component name is required', code: 'REQUIRED' }],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        const nameInput = screen.getByLabelText(/component name/i)
        const errorMessage = screen.getByText('Component name is required')

        expect(nameInput).toHaveAttribute('aria-describedby')
        expect(errorMessage).toHaveAttribute('id')
      })
    })

    it('supports keyboard navigation', () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      const manufacturerInput = screen.getByLabelText(/manufacturer/i)

      nameInput.focus()
      expect(document.activeElement).toBe(nameInput)

      fireEvent.keyDown(nameInput, { key: 'Tab' })
      expect(document.activeElement).toBe(manufacturerInput)
    })
  })

  describe('Edge Cases', () => {
    it('handles component with missing optional fields', () => {
      const minimalComponent = createMockComponent({
        description: undefined,
        unit_price: undefined,
        weight_kg: undefined,
      })

      renderWithProviders(
        <ComponentForm component={minimalComponent} isEditing={true} {...mockHandlers} />
      )

      expect(screen.getByDisplayValue(minimalComponent.name)).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toHaveValue('')
    })

    it('handles form reset when component prop changes', () => {
      const { rerender } = renderWithProviders(
        <ComponentForm component={mockComponent} isEditing={true} {...mockHandlers} />
      )

      expect(screen.getByDisplayValue(mockComponent.name)).toBeInTheDocument()

      const newComponent = createMockComponent({ name: 'New Component Name' })
      rerender(<ComponentForm component={newComponent} isEditing={true} {...mockHandlers} />)

      expect(screen.getByDisplayValue('New Component Name')).toBeInTheDocument()
    })

    it('handles API loading states for categories and types', () => {
      const { useComponentCategories, useComponentTypes } = require('../../api/componentQueries')

      vi.mocked(useComponentCategories).mockReturnValue({
        data: [],
        isLoading: true,
      })

      vi.mocked(useComponentTypes).mockReturnValue({
        data: [],
        isLoading: true,
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      expect(categorySelect).toBeInTheDocument()
      // Should show loading state or empty options
    })

    it('handles form submission with network errors', async () => {
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      const onSubmitWithError = vi.fn().mockRejectedValue(new Error('Network error'))

      renderWithProviders(
        <ComponentForm onSubmit={onSubmitWithError} onCancel={mockHandlers.onCancel} />
      )

      const nameInput = screen.getByLabelText(/component name/i)
      fireEvent.change(nameInput, { target: { value: 'Test Component' } })

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(onSubmitWithError).toHaveBeenCalled()
      })
    })

    it('handles complex form data with specifications and dimensions', async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import('../../utils')
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Fill in complex data
      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'Complex Component')

      const submitButton = screen.getByRole('button', { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockHandlers.onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'Complex Component',
          })
        )
      })
    })
  })

  describe('Integration with useComponentForm Hook', () => {
    it('integrates properly with form hook for validation', async () => {
      const { validateComponent } = await import('../../utils')

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Simulate form interaction that would trigger hook validation
      const nameInput = screen.getByLabelText(/component name/i)
      fireEvent.change(nameInput, { target: { value: 'Test' } })

      // Verify validation is called through the hook integration
      expect(validateComponent).toHaveBeenCalled()
    })

    it('handles form state synchronization', async () => {
      const user = userEvent.setup()

      renderWithProviders(
        <ComponentForm component={mockComponent} isEditing={true} {...mockHandlers} />
      )

      // Modify form data
      const nameInput = screen.getByLabelText(/component name/i)
      await user.clear(nameInput)
      await user.type(nameInput, 'Modified Name')

      // Verify form reflects the changes
      expect(nameInput).toHaveValue('Modified Name')
    })

    it('handles form dirty state tracking', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole('button', { name: /create/i })
      expect(submitButton).toBeDisabled() // Initially not dirty

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'Test')

      expect(submitButton).not.toBeDisabled() // Now dirty
    })
  })

  describe('Real-time Validation Feedback', () => {
    it('provides immediate validation feedback on field blur', async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import('../../utils')

      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Name too short', code: 'MIN_LENGTH' }],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'A')
      await user.tab() // Blur the field

      await waitFor(() => {
        expect(screen.getByText('Name too short')).toBeInTheDocument()
      })
    })

    it('shows validation success indicators', async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import('../../utils')

      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, 'Valid Component Name')

      // Check for visual success indicators (green border, checkmark, etc.)
      expect(nameInput).not.toHaveClass('border-red-500')
    })

    it('updates validation state as user types', async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import('../../utils')

      // First return error, then success
      vi.mocked(validateComponent)
        .mockReturnValueOnce({
          isValid: false,
          errors: [{ field: 'name', message: 'Name too short', code: 'MIN_LENGTH' }],
        })
        .mockReturnValueOnce({
          isValid: true,
          errors: [],
        })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)

      // Type short name
      await user.type(nameInput, 'A')
      await user.tab()

      await waitFor(() => {
        expect(screen.getByText('Name too short')).toBeInTheDocument()
      })

      // Type longer name
      await user.clear(nameInput)
      await user.type(nameInput, 'Valid Component Name')

      await waitFor(() => {
        expect(screen.queryByText('Name too short')).not.toBeInTheDocument()
      })
    })
  })
})
