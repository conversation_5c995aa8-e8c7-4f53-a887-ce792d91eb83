/**
 * Component Form Management Hook
 * Manages form state, validation, and submission for component forms
 */

import { useCallback, useEffect, useState } from 'react'
import { useCreateComponent, useUpdateComponent } from '../api/componentMutations'
import type { ComponentCreate, ComponentRead, ComponentUpdate } from '../types'
import { validateComponent, type ValidationError } from '../utils'

interface UseComponentFormOptions {
  component?: ComponentRead
  onSuccess?: (component: ComponentRead) => void
  onError?: (error: Error) => void
}

interface ComponentFormState {
  data: Partial<ComponentCreate | ComponentUpdate>
  errors: ValidationError[]
  isDirty: boolean
  isSubmitting: boolean
  isValid: boolean
}

export function useComponentForm({ component, onSuccess, onError }: UseComponentFormOptions = {}) {
  const isEditing = !!component

  // Initialize form data
  const [formState, setFormState] = useState<ComponentFormState>(() => ({
    data: component ? { ...component } : {},
    errors: [],
    isDirty: false,
    isSubmitting: false,
    isValid: false,
  }))

  // Mutations
  const createMutation = useCreateComponent({
    onSuccess: (data) => {
      setFormState((prev) => ({ ...prev, isSubmitting: false }))
      onSuccess?.(data)
    },
    onError: (error) => {
      setFormState((prev) => ({ ...prev, isSubmitting: false }))
      onError?.(error)
    },
  })

  const updateMutation = useUpdateComponent({
    onSuccess: (data) => {
      setFormState((prev) => ({ ...prev, isSubmitting: false, isDirty: false }))
      onSuccess?.(data)
    },
    onError: (error) => {
      setFormState((prev) => ({ ...prev, isSubmitting: false }))
      onError?.(error)
    },
  })

  // Validate form whenever data changes
  useEffect(() => {
    const validation = validateComponent(formState.data, isEditing)
    setFormState((prev) => ({
      ...prev,
      errors: validation.errors,
      isValid: validation.isValid,
    }))
  }, [formState.data, isEditing])

  // Handle component prop changes
  useEffect(() => {
    setFormState((prev) => ({
      ...prev,
      data: component ? { ...component } : {},
      isDirty: false,
    }))
  }, [component])

  // Update field value (supports nested paths like 'specifications.resistance')
  const updateField = useCallback((field: string, value: any) => {
    setFormState((prev) => {
      const newData = { ...prev.data }

      // Handle nested field paths
      if (field.includes('.')) {
        const [parentField, childField] = field.split('.')
        newData[parentField] = {
          ...(newData[parentField] || {}),
          [childField]: value,
        }
      } else {
        newData[field] = value
      }

      return {
        ...prev,
        data: newData,
        isDirty: true,
        errors: prev.errors.filter((error) => error.field !== field), // Clear field-specific errors
      }
    })
  }, [])

  // Update multiple fields
  const updateFields = useCallback((updates: Partial<ComponentCreate | ComponentUpdate>) => {
    setFormState((prev) => ({
      ...prev,
      data: { ...prev.data, ...updates },
      isDirty: true,
    }))
  }, [])

  // Get field error
  const getFieldError = useCallback(
    (field: string) => {
      return formState.errors.find((error) => error.field === field)?.message
    },
    [formState.errors]
  )

  // Check if field has error
  const hasFieldError = useCallback(
    (field: string) => {
      return formState.errors.some((error) => error.field === field)
    },
    [formState.errors]
  )

  // Reset form
  const reset = useCallback(() => {
    setFormState((prev) => ({
      ...prev,
      data: component ? { ...component } : {},
      errors: [],
      isDirty: false,
      isSubmitting: false,
    }))
  }, [component])

  // Submit form
  const submit = useCallback(async () => {
    if (!formState.isValid || formState.isSubmitting) {
      return
    }

    setFormState((prev) => ({ ...prev, isSubmitting: true }))

    try {
      if (isEditing && component) {
        await updateMutation.mutateAsync({
          id: component.id,
          component: formState.data as ComponentUpdate,
        })
      } else {
        await createMutation.mutateAsync(formState.data as ComponentCreate)
      }
    } catch (error) {
      // Error handling is done in mutation callbacks, but also call onError for direct submit calls
      console.error('Form submission error:', error)
      setFormState((prev) => ({ ...prev, isSubmitting: false }))
      onError?.(error as Error)
    }
  }, [
    formState.isValid,
    formState.isSubmitting,
    formState.data,
    isEditing,
    component,
    updateMutation,
    createMutation,
    onError,
  ])

  // Validate specific field
  const validateField = useCallback(
    (field: string) => {
      const fieldData = { [field]: formState.data[field as keyof typeof formState.data] }
      const validation = validateComponent(fieldData, isEditing)
      const fieldErrors = validation.errors.filter((error) => error.field === field)

      setFormState((prev) => ({
        ...prev,
        errors: [...prev.errors.filter((error) => error.field !== field), ...fieldErrors],
      }))

      return fieldErrors.length === 0
    },
    [formState, isEditing]
  )

  // Set custom error
  const setFieldError = useCallback((field: string, message: string) => {
    setFormState((prev) => ({
      ...prev,
      errors: [
        ...prev.errors.filter((error) => error.field !== field),
        { field, message, code: 'CUSTOM' },
      ],
    }))
  }, [])

  // Clear field error
  const clearFieldError = useCallback((field: string) => {
    setFormState((prev) => ({
      ...prev,
      errors: prev.errors.filter((error) => error.field !== field),
    }))
  }, [])

  return {
    // Form state
    data: formState.data,
    errors: formState.errors,
    isDirty: formState.isDirty,
    isSubmitting: formState.isSubmitting,
    isValid: formState.isValid,
    isEditing,

    // Field operations
    updateField,
    updateFields,
    getFieldError,
    hasFieldError,
    validateField,
    setFieldError,
    clearFieldError,

    // Form operations
    submit,
    reset,

    // Mutation states
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    createError: createMutation.error,
    updateError: updateMutation.error,
  }
}
