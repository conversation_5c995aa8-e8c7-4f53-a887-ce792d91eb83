/**
 * Component management utility functions
 * Includes validation, error handling, and helper functions
 */

import type { ComponentCreate, ComponentRead, ComponentUpdate } from './types'
import { COMPONENT_VALIDATION_RULES } from './types'

// Validation error interface
export interface ValidationError {
  field: string
  message: string
  code?: string
}

// Form validation result
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

// Component form validation
export function validateComponent(
  component: Partial<ComponentCreate | ComponentUpdate>,
  isUpdate: boolean = false
): ValidationResult {
  const errors: ValidationError[] = []
  const rules = COMPONENT_VALIDATION_RULES

  // Name validation
  if (!isUpdate || component.name !== undefined) {
    if (!component.name || component.name.trim().length === 0) {
      if (rules.name.required) {
        errors.push({
          field: 'name',
          message: 'Name is required',
          code: 'REQUIRED',
        })
      }
    } else {
      if (component.name.length < rules.name.minLength) {
        errors.push({
          field: 'name',
          message: `Name must be at least ${rules.name.minLength} characters`,
          code: 'MIN_LENGTH',
        })
      }
      if (component.name.length > rules.name.maxLength) {
        errors.push({
          field: 'name',
          message: `Name must not exceed ${rules.name.maxLength} characters`,
          code: 'MAX_LENGTH',
        })
      }
    }
  }

  // Manufacturer validation
  if (!isUpdate || component.manufacturer !== undefined) {
    if (!component.manufacturer || component.manufacturer.trim().length === 0) {
      if (rules.manufacturer.required) {
        errors.push({
          field: 'manufacturer',
          message: 'Manufacturer is required',
          code: 'REQUIRED',
        })
      }
    } else {
      if (component.manufacturer.length < rules.manufacturer.minLength) {
        errors.push({
          field: 'manufacturer',
          message: `Manufacturer must be at least ${rules.manufacturer.minLength} characters`,
          code: 'MIN_LENGTH',
        })
      }
      if (component.manufacturer.length > rules.manufacturer.maxLength) {
        errors.push({
          field: 'manufacturer',
          message: `Manufacturer must not exceed ${rules.manufacturer.maxLength} characters`,
          code: 'MAX_LENGTH',
        })
      }
    }
  }

  // Model number validation (only if provided)
  if (component.model_number !== undefined) {
    if (component.model_number && component.model_number.trim().length > 0) {
      if (component.model_number.length < rules.model_number.minLength) {
        errors.push({
          field: 'model_number',
          message: `Model number must be at least ${rules.model_number.minLength} characters`,
          code: 'MIN_LENGTH',
        })
      }
      if (component.model_number.length > rules.model_number.maxLength) {
        errors.push({
          field: 'model_number',
          message: `Model number must not exceed ${rules.model_number.maxLength} characters`,
          code: 'MAX_LENGTH',
        })
      }
    }
  }

  // Part number validation (only validate if provided and empty)
  if (component.part_number !== undefined) {
    if (component.part_number.trim().length === 0) {
      errors.push({
        field: 'part_number',
        message: 'Part number is required',
        code: 'REQUIRED',
      })
    }
  }

  // Description validation
  if (component.description && component.description.length > rules.description.maxLength) {
    errors.push({
      field: 'description',
      message: `Description must not exceed ${rules.description.maxLength} characters`,
      code: 'MAX_LENGTH',
    })
  }

  // Price validation (for test compatibility)
  if (component.price !== undefined) {
    const price =
      typeof component.price === 'string' ? parseFloat(component.price) : component.price

    if (isNaN(price)) {
      errors.push({
        field: 'price',
        message: 'Price must be a valid number',
        code: 'INVALID_NUMBER',
      })
    } else if (price < 0) {
      errors.push({
        field: 'price',
        message: 'Price must be positive',
        code: 'INVALID_NUMBER',
      })
    }
  }

  // Unit price validation
  if (component.unit_price !== undefined && component.unit_price !== null) {
    const price =
      typeof component.unit_price === 'string'
        ? parseFloat(component.unit_price)
        : component.unit_price

    if (isNaN(price) || price < rules.unit_price.min) {
      errors.push({
        field: 'unit_price',
        message: `Unit price must be a valid number greater than or equal to ${rules.unit_price.min}`,
        code: 'INVALID_NUMBER',
      })
    }
  }

  // Dimensions validation
  if (component.dimensions) {
    const { length, width, height } = component.dimensions

    if (length !== undefined && (typeof length !== 'number' || length <= 0)) {
      errors.push({
        field: 'dimensions.length',
        message: 'Length must be positive',
        code: 'INVALID_NUMBER',
      })
    }

    if (width !== undefined && (typeof width !== 'number' || width <= 0)) {
      errors.push({
        field: 'dimensions.width',
        message: 'Width must be positive',
        code: 'INVALID_NUMBER',
      })
    }

    if (height !== undefined && typeof height !== 'number') {
      errors.push({
        field: 'dimensions.height',
        message: 'Height must be a valid number',
        code: 'INVALID_NUMBER',
      })
    }
  }

  // Specifications validation
  if (component.specifications) {
    for (const [key, value] of Object.entries(component.specifications)) {
      if (value === '' || value === null || value === undefined) {
        errors.push({
          field: `specifications.${key}`,
          message: `${key} specification cannot be empty`,
          code: 'REQUIRED',
        })
      }
    }
  }

  // Weight validation
  if (component.weight_kg !== undefined && component.weight_kg !== null) {
    if (component.weight_kg < rules.weight_kg.min) {
      errors.push({
        field: 'weight_kg',
        message: `Weight must be greater than or equal to ${rules.weight_kg.min}`,
        code: 'INVALID_NUMBER',
      })
    }
  }

  // Currency validation
  if (component.currency && component.currency.length !== 3) {
    errors.push({
      field: 'currency',
      message: 'Currency must be a valid 3-letter ISO code (e.g., USD, EUR)',
      code: 'INVALID_FORMAT',
    })
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// API error handling utilities
export class ComponentApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ComponentApiError'
  }
}

// Parse API error response
export function parseApiError(error: any): ComponentApiError {
  if (error instanceof ComponentApiError) {
    return error
  }

  // Handle different error formats
  if (error?.response?.data) {
    const { data, status } = error.response
    return new ComponentApiError(
      data.detail || data.message || 'An error occurred',
      status,
      data.error_code,
      data
    )
  }

  if (error?.detail) {
    return new ComponentApiError(error.detail, error.status || 500, error.error_code)
  }

  return new ComponentApiError(error?.message || 'An unexpected error occurred', 500)
}

// Format error message for display
export function formatErrorMessage(error: ComponentApiError | Error): string {
  if (error instanceof ComponentApiError) {
    switch (error.status) {
      case 400:
        return `Invalid request: ${error.message}`
      case 401:
        return 'Authentication required. Please log in.'
      case 403:
        return 'You do not have permission to perform this action.'
      case 404:
        return 'The requested component was not found.'
      case 409:
        return `Conflict: ${error.message}`
      case 422:
        return `Validation error: ${error.message}`
      case 500:
        return 'A server error occurred. Please try again later.'
      default:
        return error.message
    }
  }

  return error.message || 'An unexpected error occurred'
}

// Component display utilities
export function formatComponentName(component: ComponentRead): string {
  if (component.display_name) {
    return component.display_name
  }

  if (component.full_name) {
    return component.full_name
  }

  if (component.manufacturer && component.model_number) {
    return `${component.manufacturer} ${component.model_number}`
  }

  return component.name || 'Component Name'
}

export function formatPrice(price: string | number | null, currency: string = 'USD'): string {
  if (price === null || price === undefined) {
    return 'N/A'
  }

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price

  if (isNaN(numericPrice)) {
    return 'N/A'
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(numericPrice)
}

export function formatWeight(weight: number | null): string {
  if (weight === null || weight === undefined) {
    return 'N/A'
  }

  return `${weight} kg`
}

export function formatDimensions(dimensions: any, unit: string = 'mm'): string {
  if (!dimensions) {
    return 'N/A'
  }

  const { length, width, height, diameter } = dimensions

  if (diameter) {
    return `⌀${diameter} ${unit}`
  }

  const parts = []
  if (length !== undefined && length !== null) parts.push(length)
  if (width !== undefined && width !== null) parts.push(width)
  if (height !== undefined && height !== null) parts.push(height)
  else if (length !== undefined && width !== undefined) parts.push('N/A')

  return parts.length > 0 ? `${parts.join(' × ')} ${unit}` : 'N/A'
}

// Search and filter utilities
export function buildSearchQuery(filters: any): string {
  const parts = []

  if (filters.search_term) {
    parts.push(filters.search_term)
  }

  if (filters.manufacturer) {
    parts.push(`manufacturer:${filters.manufacturer}`)
  }

  if (filters.category) {
    parts.push(`category:${filters.category}`)
  }

  if (filters.component_type) {
    parts.push(`type:${filters.component_type}`)
  }

  return parts.join(' ')
}

// Component status utilities
export function getComponentStatusColor(component: ComponentRead): string {
  if (!component.is_active) {
    return 'text-gray-500'
  }

  if (component.is_preferred) {
    return 'text-green-600'
  }

  switch (component.stock_status) {
    case 'available':
      return 'text-green-600'
    case 'low_stock':
      return 'text-yellow-600'
    case 'out_of_stock':
      return 'text-red-600'
    case 'discontinued':
      return 'text-gray-600'
    default:
      return 'text-gray-600'
  }
}

export function getComponentStatusText(component: ComponentRead): string {
  if (!component.is_active) {
    return 'Inactive'
  }

  return component.stock_status || 'Available'
}

// Price calculation utilities
export function calculateTotalPrice(
  price: string | number | null,
  quantity: number | string = 1
): number | null {
  if (price === null || price === undefined) {
    return 0
  }

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price

  const qty = typeof quantity === 'string' ? parseFloat(quantity) : quantity

  if (isNaN(numericPrice) || isNaN(qty) || qty <= 0) {
    return 0
  }

  return numericPrice * qty
}

// Component-based price calculation
export function calculateComponentTotalPrice(
  component: ComponentRead,
  quantity: number | string = 1
): number | null {
  if (!component.unit_price) {
    return null
  }

  const price =
    typeof component.unit_price === 'string'
      ? parseFloat(component.unit_price)
      : component.unit_price

  const qty = typeof quantity === 'string' ? parseFloat(quantity) : quantity

  if (isNaN(price) || isNaN(qty) || qty <= 0) {
    return null
  }

  return price * qty
}

// Component availability utilities
export function isComponentAvailable(component: ComponentRead): boolean {
  if (!component.is_active) {
    return false
  }

  if (component.stock_status === 'out_of_stock' || component.stock_status === 'discontinued') {
    return false
  }

  return true
}

// Component categorization utilities
export function getComponentCategory(component: ComponentRead): string {
  if (component.category) {
    return component.category
  }

  // Infer category from component type
  if (component.component_type) {
    const type = component.component_type.toLowerCase()
    switch (type) {
      case 'resistor':
        return 'RESISTOR'
      case 'capacitor':
        return 'CAPACITOR'
      case 'inductor':
        return 'INDUCTOR'
      case 'transistor':
        return 'TRANSISTOR'
      case 'diode':
        return 'DIODE'
      case 'ic':
        return 'IC'
      case 'connector':
        return 'CONNECTOR'
      case 'switch':
        return 'SWITCH'
      case 'relay':
        return 'RELAY'
      default:
        return 'UNKNOWN'
    }
  }

  return 'UNKNOWN'
}

// Specification normalization utilities
export function normalizeSpecifications(specifications: any): Record<string, string> {
  if (!specifications || typeof specifications !== 'object') {
    return {}
  }

  const normalized: Record<string, string> = {}

  for (const [key, value] of Object.entries(specifications)) {
    if (value !== null && value !== undefined) {
      let normalizedValue = String(value)

      // Normalize common electrical values
      if (key === 'resistance') {
        normalizedValue = normalizeResistanceValue(normalizedValue)
      } else if (key === 'tolerance') {
        normalizedValue = normalizeToleranceValue(normalizedValue)
      }

      normalized[key] = normalizedValue
    }
  }

  return normalized
}

// Helper function to normalize resistance values
function normalizeResistanceValue(value: string): string {
  const trimmed = value.trim().toUpperCase()

  // Convert K, M, G suffixes to numeric values
  if (trimmed.endsWith('K') || trimmed.endsWith('KΩ')) {
    const num = parseFloat(trimmed.replace(/[KΩ]/g, ''))
    if (!isNaN(num)) {
      return String(num * 1000)
    }
  } else if (trimmed.endsWith('M') || trimmed.endsWith('MΩ')) {
    const num = parseFloat(trimmed.replace(/[MΩ]/g, ''))
    if (!isNaN(num)) {
      return String(num * 1000000)
    }
  } else if (trimmed.endsWith('G') || trimmed.endsWith('GΩ')) {
    const num = parseFloat(trimmed.replace(/[GΩ]/g, ''))
    if (!isNaN(num)) {
      return String(num * 1000000000)
    }
  }

  return value
}

// Helper function to normalize tolerance values
function normalizeToleranceValue(value: string): string {
  const trimmed = value.trim()

  // Remove ± symbol if present
  if (trimmed.startsWith('±')) {
    return trimmed.substring(1)
  }

  return trimmed
}
