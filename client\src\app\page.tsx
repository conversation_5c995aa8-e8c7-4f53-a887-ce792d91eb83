'use client'

import { <PERSON><PERSON> } from '@/components/common/Footer'
import { Header } from '@/components/common/Header'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import Link from 'next/link'

export default function LandingPage() {
  const { isAuthenticated, user } = useAuth()

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <Header />

      {/* Hero Section */}
      <section className="hero-gradient text-white">
        <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-bold md:text-6xl">
              Professional Electrical Design
              <span className="block text-blue-200">Made Simple</span>
            </h1>
            <p className="mx-auto mb-8 max-w-3xl text-xl text-blue-100 md:text-2xl">
              An engineering-grade application for industrial electrical design, heat tracing
              calculations, and project management.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              {isAuthenticated ? (
                <>
                  <Button size="xl" className="bg-white text-blue-600 hover:bg-blue-50" asChild>
                    <Link href="/dashboard">Go to Dashboard</Link>
                  </Button>
                  <div className="py-3 text-lg text-blue-100">Welcome back, {user?.name}!</div>
                </>
              ) : (
                <>
                  <Button size="xl" className="bg-white text-blue-600 hover:bg-blue-50" asChild>
                    <Link href="/login">Start Designing</Link>
                  </Button>
                  <Button
                    size="xl"
                    variant="outline"
                    className="border-2 border-white text-white hover:bg-white hover:text-blue-600"
                    asChild
                  >
                    <Link href="/dashboard">View Dashboard</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-50 py-20">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
              Engineering-Grade Features
            </h2>
            <p className="mx-auto max-w-2xl text-xl text-gray-600">
              Built for professional electrical engineers with industry-standard calculations and
              workflows.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Feature 1 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Heat Tracing Design</h3>
              <p className="text-gray-600">
                Advanced heat tracing calculations with support for multiple pipe configurations and
                environmental conditions.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Load Calculations</h3>
              <p className="text-gray-600">
                Comprehensive electrical load analysis with automatic sizing recommendations and
                safety factors.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
                <svg
                  className="h-6 w-6 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Project Management</h3>
              <p className="text-gray-600">
                Organize and track multiple projects with detailed documentation and progress
                monitoring.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Cable Sizing</h3>
              <p className="text-gray-600">
                Automated cable sizing calculations based on current capacity, voltage drop, and
                installation methods.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                <svg
                  className="h-6 w-6 text-yellow-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Safety Compliance</h3>
              <p className="text-gray-600">
                Built-in safety checks and compliance verification for electrical codes and
                standards.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="card-base-style">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100">
                <svg
                  className="h-6 w-6 text-indigo-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold text-gray-900">Documentation</h3>
              <p className="text-gray-600">
                Generate professional reports and documentation with calculations, diagrams, and
                specifications.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-20">
        <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
          <h2 className="mb-4 text-3xl font-bold text-white md:text-4xl">
            Ready to Start Your Next Project?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-xl text-blue-100">
            Join professional engineers who trust Ultimate Electrical Designer for their critical
            projects.
          </p>
          {isAuthenticated ? (
            <Button size="xl" className="bg-white text-blue-600 hover:bg-blue-50" asChild>
              <Link href="/dashboard">Continue to Dashboard</Link>
            </Button>
          ) : (
            <Button size="xl" className="bg-white text-blue-600 hover:bg-blue-50" asChild>
              <Link href="/login">Get Started Today</Link>
            </Button>
          )}
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  )
}
