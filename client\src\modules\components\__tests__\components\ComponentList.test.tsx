/**
 * ComponentList Unit Tests
 * Tests the ComponentList component with pagination, view modes, and interactions
 */

import {
  createMockComponentList,
  createMockPaginatedResponse,
  mockComponentPaginatedResponse,
  renderWithProviders,
} from '@/test/utils'
import { fireEvent, screen } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComponentList } from '../../components/ComponentList'

// Mock ComponentCard since we're testing ComponentList
vi.mock('../../components/ComponentCard', () => ({
  ComponentCard: ({ component, onSelect, onEdit, onDelete, onView, onTogglePreferred }: any) => (
    <div data-testid={`component-card-${component.id}`}>
      <span>{component.name}</span>
      <button onClick={() => onSelect?.(component)}>Select</button>
      <button onClick={() => onEdit?.(component)}>Edit</button>
      <button onClick={() => onDelete?.(component)}>Delete</button>
      <button onClick={() => onView?.(component)}>View</button>
      <button onClick={() => onTogglePreferred?.(component)}>Toggle Preferred</button>
    </div>
  ),
  ComponentCardSkeleton: ({ compact }: any) => (
    <div data-testid="component-card-skeleton" className={compact ? 'compact' : ''}>
      Loading component...
    </div>
  ),
}))

describe('ComponentList', () => {
  const mockHandlers = {
    onViewModeChange: vi.fn(),
    onPageChange: vi.fn(),
    onPageSizeChange: vi.fn(),
    onComponentSelect: vi.fn(),
    onComponentEdit: vi.fn(),
    onComponentDelete: vi.fn(),
    onComponentView: vi.fn(),
    onTogglePreferred: vi.fn(),
    onSelectionChange: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders component list with data', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      expect(screen.getByTestId('component-card-1')).toBeInTheDocument()
      expect(screen.getByTestId('component-card-2')).toBeInTheDocument()
      expect(screen.getByTestId('component-card-3')).toBeInTheDocument()
    })

    it('shows loading state', () => {
      renderWithProviders(<ComponentList isLoading={true} {...mockHandlers} />)

      expect(screen.getByText('Loading components...')).toBeInTheDocument()
    })

    it('shows error state', () => {
      const error = new Error('Failed to load components')

      renderWithProviders(<ComponentList error={error} {...mockHandlers} />)

      expect(screen.getByText('Error loading components')).toBeInTheDocument()
      expect(screen.getByText('Failed to load components')).toBeInTheDocument()
    })

    it('shows empty state when no data', () => {
      const emptyResponse = createMockPaginatedResponse([])

      renderWithProviders(<ComponentList data={emptyResponse} {...mockHandlers} />)

      expect(screen.getByText('No components found')).toBeInTheDocument()
    })

    it('renders with different view modes', () => {
      const { rerender } = renderWithProviders(
        <ComponentList data={mockComponentPaginatedResponse} viewMode="grid" {...mockHandlers} />
      )

      // Grid view should have grid layout
      const container = screen.getByTestId('component-list-container')
      expect(container).toHaveClass(
        'grid-cols-1',
        'md:grid-cols-2',
        'lg:grid-cols-3',
        'xl:grid-cols-4'
      )

      // Switch to list view
      rerender(
        <ComponentList data={mockComponentPaginatedResponse} viewMode="list" {...mockHandlers} />
      )

      expect(container).toHaveClass('grid-cols-1')
    })
  })

  describe('View Mode Controls', () => {
    it('renders view mode toggle buttons', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      expect(screen.getByRole('button', { name: /grid view/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /list view/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /table view/i })).toBeInTheDocument()
    })

    it('calls onViewModeChange when view mode is changed', () => {
      renderWithProviders(
        <ComponentList data={mockComponentPaginatedResponse} viewMode="grid" {...mockHandlers} />
      )

      const listViewButton = screen.getByRole('button', { name: /list view/i })
      fireEvent.click(listViewButton)

      expect(mockHandlers.onViewModeChange).toHaveBeenCalledWith('list')
    })

    it('highlights active view mode', () => {
      renderWithProviders(
        <ComponentList data={mockComponentPaginatedResponse} viewMode="grid" {...mockHandlers} />
      )

      const gridViewButton = screen.getByRole('button', { name: /grid view/i })
      expect(gridViewButton).toHaveClass('bg-blue-100')
    })
  })

  describe('Pagination', () => {
    it('renders pagination controls', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /previous page/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /next page/i })).toBeInTheDocument()
    })

    it('calls onPageChange when page is changed', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      const nextButton = screen.getByRole('button', { name: /next page/i })
      fireEvent.click(nextButton)

      expect(mockHandlers.onPageChange).toHaveBeenCalledWith(2)
    })

    it('disables previous button on first page', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      const prevButton = screen.getByRole('button', { name: /previous page/i })
      expect(prevButton).toBeDisabled()
    })

    it('renders page size selector', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      expect(screen.getByRole('combobox', { name: /items per page/i })).toBeInTheDocument()
    })

    it('calls onPageSizeChange when page size is changed', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      const pageSizeSelect = screen.getByRole('combobox', { name: /items per page/i })
      fireEvent.change(pageSizeSelect, { target: { value: '20' } })

      expect(mockHandlers.onPageSizeChange).toHaveBeenCalledWith(20)
    })
  })

  describe('Component Interactions', () => {
    it('forwards component selection events', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      const selectButton = screen.getAllByText('Select')[0]
      fireEvent.click(selectButton)

      expect(mockHandlers.onComponentSelect).toHaveBeenCalledWith(
        mockComponentPaginatedResponse.items[0]
      )
    })

    it('forwards component edit events', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      const editButton = screen.getAllByText('Edit')[0]
      fireEvent.click(editButton)

      expect(mockHandlers.onComponentEdit).toHaveBeenCalledWith(
        mockComponentPaginatedResponse.items[0]
      )
    })

    it('forwards component delete events', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      const deleteButton = screen.getAllByText('Delete')[0]
      fireEvent.click(deleteButton)

      expect(mockHandlers.onComponentDelete).toHaveBeenCalledWith(
        mockComponentPaginatedResponse.items[0]
      )
    })

    it('forwards component view events', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      const viewButton = screen.getAllByText('View')[0]
      fireEvent.click(viewButton)

      expect(mockHandlers.onComponentView).toHaveBeenCalledWith(
        mockComponentPaginatedResponse.items[0]
      )
    })

    it('forwards toggle preferred events', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      const toggleButton = screen.getAllByText('Toggle Preferred')[0]
      fireEvent.click(toggleButton)

      expect(mockHandlers.onTogglePreferred).toHaveBeenCalledWith(
        mockComponentPaginatedResponse.items[0]
      )
    })
  })

  describe('Selection Management', () => {
    it('handles bulk selection', () => {
      renderWithProviders(
        <ComponentList
          data={mockComponentPaginatedResponse}
          selectedComponents={[1, 2]}
          {...mockHandlers}
        />
      )

      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
      expect(selectAllCheckbox).toBeInTheDocument()
    })

    it('calls onSelectionChange when selection changes', () => {
      renderWithProviders(
        <ComponentList
          data={mockComponentPaginatedResponse}
          selectedComponents={[]}
          {...mockHandlers}
        />
      )

      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
      fireEvent.click(selectAllCheckbox)

      expect(mockHandlers.onSelectionChange).toHaveBeenCalledWith([1, 2, 3])
    })

    it('shows selection count when components are selected', () => {
      renderWithProviders(
        <ComponentList
          data={mockComponentPaginatedResponse}
          selectedComponents={[1, 2]}
          {...mockHandlers}
        />
      )

      expect(screen.getByText('2 components selected')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} {...mockHandlers} />)

      expect(screen.getByRole('region', { name: /component list/i })).toBeInTheDocument()
    })

    it('supports keyboard navigation for pagination', () => {
      const paginatedData = createMockPaginatedResponse(createMockComponentList(5), 1, 2)

      renderWithProviders(<ComponentList data={paginatedData} {...mockHandlers} />)

      const nextButton = screen.getByRole('button', { name: /next page/i })
      fireEvent.keyDown(nextButton, { key: 'Enter' })

      expect(mockHandlers.onPageChange).toHaveBeenCalledWith(2)
    })
  })

  describe('Edge Cases', () => {
    it('handles undefined data gracefully', () => {
      renderWithProviders(<ComponentList data={undefined} {...mockHandlers} />)

      expect(screen.getByText('No components found')).toBeInTheDocument()
    })

    it('handles empty selectedComponents array', () => {
      renderWithProviders(
        <ComponentList
          data={mockComponentPaginatedResponse}
          selectedComponents={[]}
          {...mockHandlers}
        />
      )

      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
      expect(selectAllCheckbox).not.toBeChecked()
    })

    it('handles missing optional handlers', () => {
      renderWithProviders(<ComponentList data={mockComponentPaginatedResponse} />)

      // Should render without errors
      expect(screen.getByTestId('component-card-1')).toBeInTheDocument()
    })
  })
})
