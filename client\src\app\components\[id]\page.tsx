'use client'

/**
 * Component Details Page
 * Displays detailed information about a specific component
 */

import React from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, Trash2 } from 'lucide-react'
import { ComponentDetails } from '@/modules/components'
import { useComponent } from '@/modules/components/api/componentQueries'
import { useDeleteComponent } from '@/modules/components/api/componentMutations'

export default function ComponentDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const componentId = parseInt(params.id as string)

  // Fetch component data
  const { data: component, isLoading, error } = useComponent(componentId)

  // Delete mutation
  const deleteMutation = useDeleteComponent({
    onSuccess: () => {
      router.push('/components')
    },
    onError: (error) => {
      alert(`Failed to delete component: ${error.message}`)
    },
  })

  // Handle actions
  const handleEdit = () => {
    router.push(`/components/${componentId}/edit`)
  }

  const handleDelete = () => {
    if (!component) return

    const confirmed = confirm(
      `Are you sure you want to delete "${component.name}"? This action cannot be undone.`
    )

    if (confirmed) {
      deleteMutation.mutate(componentId)
    }
  }

  const handleTogglePreferred = () => {
    if (!component) return
    // Handle toggle preferred logic here
    console.log('Toggling preferred for component:', component.id)
  }

  const handleBack = () => {
    router.back()
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="space-y-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="rounded-lg border border-gray-200 bg-white p-6">
                <div className="animate-pulse">
                  <div className="mb-4 h-6 w-1/3 rounded bg-gray-200"></div>
                  <div className="space-y-3">
                    <div className="h-4 w-full rounded bg-gray-200"></div>
                    <div className="h-4 w-3/4 rounded bg-gray-200"></div>
                    <div className="h-4 w-1/2 rounded bg-gray-200"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-12">
            <div className="text-center">
              <div className="mb-4 text-red-500">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">Component not found</h3>
              <p className="mb-6 text-gray-600">
                {error.message || 'The requested component could not be found.'}
              </p>
              <Button onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Component not found
  if (!component) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-12">
            <div className="text-center">
              <h3 className="mb-2 text-lg font-medium text-gray-900">Component not found</h3>
              <p className="mb-6 text-gray-600">
                The component with ID {componentId} does not exist.
              </p>
              <Button onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>

              <div>
                <h1 className="text-xl font-bold text-gray-900">Component Details</h1>
                <p className="text-sm text-gray-600">
                  {component.manufacturer} • {component.model_number}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <ComponentDetails
          component={component}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onTogglePreferred={handleTogglePreferred}
        />
      </div>
    </div>
  )
}
