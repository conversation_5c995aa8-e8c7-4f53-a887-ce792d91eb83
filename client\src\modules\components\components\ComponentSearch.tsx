'use client'

/**
 * ComponentSearch - Search input component with autocomplete suggestions
 * Provides real-time search with debouncing and suggestion dropdown
 */

import { Button } from '@/components/ui/button'
import { Clock, Loader2, Search, X } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import { useComponentSuggestions } from '../api/componentQueries'

export interface ComponentSearchProps {
  value?: string
  placeholder?: string
  field?: 'name' | 'manufacturer' | 'part_number' | 'model'
  showSuggestions?: boolean
  recentSearches?: string[]
  onSearch?: (query: string) => void
  onChange?: (value: string) => void
  onClear?: () => void
  className?: string
}

export function ComponentSearch({
  value = '',
  placeholder = 'Search components...',
  field = 'name',
  showSuggestions = true,
  recentSearches = [],
  onSearch,
  onChange,
  onClear,
  className = '',
}: ComponentSearchProps) {
  const [inputValue, setInputValue] = useState(value)
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Debounced search query
  const [debouncedQuery, setDebouncedQuery] = useState('')

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(inputValue)
    }, 300)

    return () => clearTimeout(timer)
  }, [inputValue])

  // Fetch suggestions
  const { data: suggestions = [], isLoading: loadingSuggestions } = useComponentSuggestions(
    debouncedQuery,
    field,
    10,
    { enabled: showSuggestions && debouncedQuery.length >= 2 }
  )

  // Combine suggestions with recent searches
  const allSuggestions = React.useMemo(() => {
    const items = []

    // Add recent searches if no query or query matches
    if (debouncedQuery.length < 2 && recentSearches.length > 0) {
      items.push(
        ...recentSearches
          .filter((search) => search.toLowerCase().includes(inputValue.toLowerCase()))
          .slice(0, 5)
          .map((search) => ({ value: search, type: 'recent' as const }))
      )
    }

    // Add API suggestions
    if (suggestions.length > 0) {
      items.push(
        ...suggestions.map((suggestion) => ({ value: suggestion, type: 'suggestion' as const }))
      )
    }

    return items
  }, [suggestions, recentSearches, debouncedQuery, inputValue])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    onChange?.(newValue)
    setSelectedIndex(-1)
    setShowDropdown(true)
  }

  // Handle search submission
  const handleSearch = (query?: string) => {
    const searchQuery = query || inputValue
    onSearch?.(searchQuery)
    setShowDropdown(false)
    inputRef.current?.blur()
  }

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: string) => {
    setInputValue(suggestion)
    onChange?.(suggestion)
    handleSearch(suggestion)
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showDropdown || allSuggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch()
      }
      return
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex((prev) => (prev < allSuggestions.length - 1 ? prev + 1 : prev))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : -1))
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          handleSuggestionSelect(allSuggestions[selectedIndex].value)
        } else {
          handleSearch()
        }
        break
      case 'Escape':
        setShowDropdown(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Handle clear
  const handleClear = () => {
    setInputValue('')
    onChange?.('')
    onClear?.()
    setShowDropdown(false)
    inputRef.current?.focus()
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <Search
          data-testid="search-icon"
          className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400"
        />
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowDropdown(true)}
          placeholder={placeholder}
          aria-label="Search components"
          aria-autocomplete="list"
          className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-10 focus:border-transparent focus:ring-2 focus:ring-blue-500"
        />

        {inputValue && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            aria-label="Clear search"
            className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 transform p-0 hover:bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Suggestions dropdown */}
      {showDropdown && (showSuggestions || recentSearches.length > 0) && (
        <div
          ref={dropdownRef}
          className="absolute z-50 mt-1 max-h-60 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"
        >
          {loadingSuggestions && debouncedQuery.length >= 2 && (
            <div className="flex items-center justify-center py-3">
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
              <span className="ml-2 text-sm text-gray-500">Loading suggestions...</span>
            </div>
          )}

          {allSuggestions.length > 0 ? (
            <ul className="py-1">
              {allSuggestions.map((item, index) => (
                <li key={`${item.type}-${item.value}-${index}`}>
                  <button
                    className={`flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100 ${
                      index === selectedIndex ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                    }`}
                    onClick={() => handleSuggestionSelect(item.value)}
                  >
                    {item.type === 'recent' ? (
                      <Clock className="mr-2 h-4 w-4 text-gray-400" />
                    ) : (
                      <Search className="mr-2 h-4 w-4 text-gray-400" />
                    )}
                    <span className="truncate">{item.value}</span>
                    {item.type === 'recent' && (
                      <span className="ml-auto text-xs text-gray-400">Recent</span>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            debouncedQuery.length >= 2 &&
            !loadingSuggestions && (
              <div className="px-4 py-3 text-center text-sm text-gray-500">
                No suggestions found
              </div>
            )
          )}

          {/* Search action */}
          {inputValue && (
            <div className="border-t border-gray-100 p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearch()}
                className="w-full justify-start text-blue-600 hover:bg-blue-50 hover:text-blue-700"
              >
                <Search className="mr-2 h-4 w-4" />
                Search for &quot;{inputValue}&quot;
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
