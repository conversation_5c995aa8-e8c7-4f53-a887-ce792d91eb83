/**
 * Component Utilities Tests
 * Tests validation, formatting, and helper functions
 */

import { describe, it, expect } from 'vitest'
import {
  validateComponent,
  formatComponentName,
  formatPrice,
  formatWeight,
  formatDimensions,
  getComponentStatusColor,
  getComponentStatusText,
  calculateTotalPrice,
  isComponentAvailable,
  getComponentCategory,
  normalizeSpecifications,
} from '../utils'
import { mockComponent, createMockComponent } from '@/test/utils'
import { ComponentCategoryType, ComponentType } from '../types'

describe('Component Utilities', () => {
  describe('validateComponent', () => {
    it('validates a valid component', () => {
      const validComponent = {
        name: 'Test Resistor',
        manufacturer: 'Test Electronics',
        part_number: 'TR-001',
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
        price: '0.15',
      }

      const result = validateComponent(validComponent)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('validates required fields', () => {
      const invalidComponent = {}

      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: 'Name is required',
      })
      expect(result.errors).toContainEqual({
        field: 'manufacturer',
        message: 'Manufacturer is required',
      })
    })

    it('validates price format', () => {
      const invalidComponent = {
        name: 'Test',
        manufacturer: 'Test',
        part_number: 'TEST',
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
        price: 'invalid-price',
      }

      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'price',
        message: 'Price must be a valid number',
      })
    })

    it('validates negative prices', () => {
      const invalidComponent = {
        name: 'Test',
        manufacturer: 'Test',
        part_number: 'TEST',
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
        price: '-1.00',
      }

      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'price',
        message: 'Price must be positive',
      })
    })

    it('validates part number uniqueness', () => {
      const duplicateComponent = {
        name: 'Test',
        manufacturer: 'Test',
        part_number: '', // Empty part number
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
      }

      const result = validateComponent(duplicateComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'part_number',
        message: 'Part number is required',
      })
    })

    it('validates dimensions', () => {
      const invalidComponent = {
        name: 'Test',
        manufacturer: 'Test',
        part_number: 'TEST',
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
        dimensions: {
          length: -1,
          width: 0,
          height: 'invalid' as any,
        },
      }

      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'dimensions.length',
        message: 'Length must be positive',
      })
      expect(result.errors).toContainEqual({
        field: 'dimensions.width',
        message: 'Width must be positive',
      })
      expect(result.errors).toContainEqual({
        field: 'dimensions.height',
        message: 'Height must be a valid number',
      })
    })

    it('validates specifications format', () => {
      const invalidComponent = {
        name: 'Test',
        manufacturer: 'Test',
        part_number: 'TEST',
        category: 'RESISTOR' as ComponentCategoryType,
        component_type: 'RESISTOR' as ComponentType,
        specifications: {
          resistance: '', // Empty value
          tolerance: 'invalid-tolerance',
        },
      }

      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors.some((e) => e.field.startsWith('specifications'))).toBe(true)
    })
  })

  describe('formatComponentName', () => {
    it('returns display_name when available', () => {
      const component = createMockComponent({
        display_name: 'Custom Display Name',
        full_name: 'Full Component Name',
        name: 'Component Name',
      })

      const result = formatComponentName(component)

      expect(result).toBe('Custom Display Name')
    })

    it('returns full_name when display_name is not available', () => {
      const component = createMockComponent({
        display_name: undefined,
        full_name: 'Full Component Name',
        name: 'Component Name',
      })

      const result = formatComponentName(component)

      expect(result).toBe('Full Component Name')
    })

    it('returns formatted name when neither display_name nor full_name is available', () => {
      const component = createMockComponent({
        display_name: undefined,
        full_name: undefined,
        name: 'Component Name',
        manufacturer: 'Test Manufacturer',
        model_number: 'TM001',
      })

      const result = formatComponentName(component)

      expect(result).toBe('Test Manufacturer TM001')
    })

    it('handles missing model_number', () => {
      const component = createMockComponent({
        display_name: undefined,
        full_name: undefined,
        name: 'Component Name',
        manufacturer: 'Test Manufacturer',
        model_number: undefined,
      })

      const result = formatComponentName(component)

      expect(result).toBe('Component Name')
    })
  })

  describe('formatPrice', () => {
    it('formats valid price with default currency', () => {
      const result = formatPrice('1.50')

      expect(result).toBe('$1.50')
    })

    it('formats valid price with specified currency', () => {
      const result = formatPrice('1.50', 'EUR')

      expect(result).toBe('€1.50')
    })

    it('formats numeric price', () => {
      const result = formatPrice(1.5)

      expect(result).toBe('$1.50')
    })

    it('handles null price', () => {
      const result = formatPrice(null)

      expect(result).toBe('N/A')
    })

    it('handles undefined price', () => {
      const result = formatPrice(undefined as any)

      expect(result).toBe('N/A')
    })

    it('handles invalid string price', () => {
      const result = formatPrice('invalid')

      expect(result).toBe('N/A')
    })

    it('formats large prices correctly', () => {
      const result = formatPrice('1234.56')

      expect(result).toBe('$1,234.56')
    })

    it('formats small prices correctly', () => {
      const result = formatPrice('0.001')

      expect(result).toBe('$0.00')
    })
  })

  describe('formatWeight', () => {
    it('formats valid weight', () => {
      const result = formatWeight(1.5)

      expect(result).toBe('1.5 kg')
    })

    it('handles null weight', () => {
      const result = formatWeight(null)

      expect(result).toBe('N/A')
    })

    it('handles undefined weight', () => {
      const result = formatWeight(undefined as any)

      expect(result).toBe('N/A')
    })

    it('formats zero weight', () => {
      const result = formatWeight(0)

      expect(result).toBe('0 kg')
    })

    it('formats very small weights', () => {
      const result = formatWeight(0.001)

      expect(result).toBe('0.001 kg')
    })
  })

  describe('formatDimensions', () => {
    it('formats complete dimensions', () => {
      const dimensions = {
        length: 10.5,
        width: 5.2,
        height: 3.1,
      }

      const result = formatDimensions(dimensions)

      expect(result).toBe('10.5 × 5.2 × 3.1 mm')
    })

    it('handles missing dimensions', () => {
      const result = formatDimensions(null)

      expect(result).toBe('N/A')
    })

    it('handles partial dimensions', () => {
      const dimensions = {
        length: 10.5,
        width: 5.2,
      }

      const result = formatDimensions(dimensions as any)

      expect(result).toBe('10.5 × 5.2 × N/A mm')
    })

    it('formats with different units', () => {
      const dimensions = {
        length: 1.5,
        width: 2.0,
        height: 0.5,
      }

      const result = formatDimensions(dimensions, 'cm')

      expect(result).toBe('1.5 × 2 × 0.5 cm')
    })
  })

  describe('getComponentStatusColor', () => {
    it('returns gray for inactive components', () => {
      const component = createMockComponent({ is_active: false })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-gray-500')
    })

    it('returns green for preferred components', () => {
      const component = createMockComponent({
        is_active: true,
        is_preferred: true,
      })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-green-600')
    })

    it('returns green for available components', () => {
      const component = createMockComponent({
        is_active: true,
        is_preferred: false,
        stock_status: 'available',
      })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-green-600')
    })

    it('returns yellow for low stock components', () => {
      const component = createMockComponent({
        is_active: true,
        is_preferred: false,
        stock_status: 'low_stock',
      })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-yellow-600')
    })

    it('returns red for out of stock components', () => {
      const component = createMockComponent({
        is_active: true,
        is_preferred: false,
        stock_status: 'out_of_stock',
      })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-red-600')
    })

    it('returns gray for discontinued components', () => {
      const component = createMockComponent({
        is_active: true,
        is_preferred: false,
        stock_status: 'discontinued',
      })

      const result = getComponentStatusColor(component)

      expect(result).toBe('text-gray-600')
    })
  })

  describe('getComponentStatusText', () => {
    it('returns "Inactive" for inactive components', () => {
      const component = createMockComponent({ is_active: false })

      const result = getComponentStatusText(component)

      expect(result).toBe('Inactive')
    })

    it('returns stock status for active components', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: 'low_stock',
      })

      const result = getComponentStatusText(component)

      expect(result).toBe('low_stock')
    })

    it('returns "Available" for components without stock status', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: undefined,
      })

      const result = getComponentStatusText(component)

      expect(result).toBe('Available')
    })
  })

  describe('calculateTotalPrice', () => {
    it('calculates total price for single component', () => {
      const result = calculateTotalPrice('1.50', 10)

      expect(result).toBe(15.0)
    })

    it('handles string quantities', () => {
      const result = calculateTotalPrice('2.25', '5')

      expect(result).toBe(11.25)
    })

    it('handles null price', () => {
      const result = calculateTotalPrice(null, 10)

      expect(result).toBe(0)
    })

    it('handles invalid price', () => {
      const result = calculateTotalPrice('invalid', 10)

      expect(result).toBe(0)
    })

    it('handles zero quantity', () => {
      const result = calculateTotalPrice('1.50', 0)

      expect(result).toBe(0)
    })

    it('handles negative quantity', () => {
      const result = calculateTotalPrice('1.50', -5)

      expect(result).toBe(0)
    })
  })

  describe('isComponentAvailable', () => {
    it('returns false for inactive components', () => {
      const component = createMockComponent({ is_active: false })

      const result = isComponentAvailable(component)

      expect(result).toBe(false)
    })

    it('returns false for out of stock components', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: 'out_of_stock',
      })

      const result = isComponentAvailable(component)

      expect(result).toBe(false)
    })

    it('returns false for discontinued components', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: 'discontinued',
      })

      const result = isComponentAvailable(component)

      expect(result).toBe(false)
    })

    it('returns true for available components', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: 'available',
      })

      const result = isComponentAvailable(component)

      expect(result).toBe(true)
    })

    it('returns true for low stock components', () => {
      const component = createMockComponent({
        is_active: true,
        stock_status: 'low_stock',
      })

      const result = isComponentAvailable(component)

      expect(result).toBe(true)
    })
  })

  describe('getComponentCategory', () => {
    it('returns category from component', () => {
      const component = createMockComponent({
        category: 'RESISTOR' as ComponentCategoryType,
      })

      const result = getComponentCategory(component)

      expect(result).toBe('RESISTOR')
    })

    it('infers category from component type', () => {
      const component = createMockComponent({
        category: undefined,
        component_type: 'RESISTOR' as ComponentType,
      })

      const result = getComponentCategory(component)

      expect(result).toBe('RESISTOR')
    })

    it('returns unknown for unrecognized types', () => {
      const component = createMockComponent({
        category: undefined,
        component_type: 'UNKNOWN' as any,
      })

      const result = getComponentCategory(component)

      expect(result).toBe('UNKNOWN')
    })
  })

  describe('normalizeSpecifications', () => {
    it('normalizes specification values', () => {
      const specs = {
        resistance: '1K',
        tolerance: '±5%',
        power_rating: '0.25W',
      }

      const result = normalizeSpecifications(specs)

      expect(result.resistance).toBe('1000')
      expect(result.tolerance).toBe('5%')
      expect(result.power_rating).toBe('0.25W')
    })

    it('handles empty specifications', () => {
      const result = normalizeSpecifications({})

      expect(result).toEqual({})
    })

    it('handles null specifications', () => {
      const result = normalizeSpecifications(null)

      expect(result).toEqual({})
    })

    it('preserves unknown specifications', () => {
      const specs = {
        custom_spec: 'custom_value',
        unknown_spec: 'unknown_value',
      }

      const result = normalizeSpecifications(specs)

      expect(result.custom_spec).toBe('custom_value')
      expect(result.unknown_spec).toBe('unknown_value')
    })
  })
})
