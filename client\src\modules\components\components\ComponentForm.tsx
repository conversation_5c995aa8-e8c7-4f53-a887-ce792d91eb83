'use client';

/**
 * ComponentForm - Form component for creating and editing components
 * <PERSON>les validation and submission for component CRUD operations
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Save, X } from 'lucide-react';
import React, { useState } from 'react';
import { useComponentCategories, useComponentTypes } from '../api/componentQueries';
import type { ComponentCreate, ComponentRead, ComponentUpdate } from '../types';
import { validateComponent, type ValidationError } from '../utils';

export interface ComponentFormProps {
  component?: ComponentRead;
  isEditing?: boolean;
  isLoading?: boolean;
  onSubmit: (data: ComponentCreate | ComponentUpdate) => void;
  onCancel: () => void;
  className?: string;
}

export function ComponentForm({
  component,
  isEditing = false,
  isLoading = false,
  onSubmit,
  onCancel,
  className = '',
}: ComponentFormProps) {
  const [formData, setFormData] = useState<Partial<ComponentCreate | ComponentUpdate>>({
    name: component?.name || '',
    manufacturer: component?.manufacturer || '',
    model_number: component?.model_number || '',
    description: component?.description || '',
    component_category_id: component?.component_category_id || null,
    component_type_id: component?.component_type_id || null,
    unit_price: component?.unit_price || null,
    currency: component?.currency || 'USD',
    supplier: component?.supplier || '',
    part_number: component?.part_number || '',
    weight_kg: component?.weight_kg || null,
    is_active: component?.is_active ?? true,
    is_preferred: component?.is_preferred ?? false,
    stock_status: component?.stock_status || 'available',
  });

  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isDirty, setIsDirty] = useState(false);

  // Fetch options
  const { data: categories = [] } = useComponentCategories();
  const { data: types = [] } = useComponentTypes();

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
    
    // Clear field-specific errors
    setErrors(prev => prev.filter(error => error.field !== field));
  };

  // Validate form
  const validateForm = () => {
    const validation = validateComponent(formData, isEditing);
    setErrors(validation.errors);
    return validation.isValid;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData as ComponentCreate | ComponentUpdate);
  };

  // Get field error
  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>
          {isEditing ? 'Edit Component' : 'Create New Component'}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="component-name" className="block text-sm font-medium text-gray-700 mb-1">
                Component Name *
              </label>
              <input
                id="component-name"
                type="text"
                value={formData.name || ''}
                onChange={(e) => handleChange('name', e.target.value)}
                className={`w-full border rounded-lg px-3 py-2 ${
                  getFieldError('name') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter component name"
              />
              {getFieldError('name') && (
                <p className="text-red-500 text-xs mt-1">{getFieldError('name')}</p>
              )}
            </div>

            <div>
              <label htmlFor="manufacturer" className="block text-sm font-medium text-gray-700 mb-1">
                Manufacturer *
              </label>
              <input
                id="manufacturer"
                type="text"
                value={formData.manufacturer || ''}
                onChange={(e) => handleChange('manufacturer', e.target.value)}
                className={`w-full border rounded-lg px-3 py-2 ${
                  getFieldError('manufacturer') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter manufacturer"
              />
              {getFieldError('manufacturer') && (
                <p className="text-red-500 text-xs mt-1">{getFieldError('manufacturer')}</p>
              )}
            </div>

            <div>
              <label htmlFor="model-number" className="block text-sm font-medium text-gray-700 mb-1">
                Model Number *
              </label>
              <input
                id="model-number"
                type="text"
                value={formData.model_number || ''}
                onChange={(e) => handleChange('model_number', e.target.value)}
                className={`w-full border rounded-lg px-3 py-2 ${
                  getFieldError('model_number') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter model number"
              />
              {getFieldError('model_number') && (
                <p className="text-red-500 text-xs mt-1">{getFieldError('model_number')}</p>
              )}
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                value={formData.component_category_id || ''}
                onChange={(e) => handleChange('component_category_id', e.target.value ? Number(e.target.value) : null)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value="">Select category</option>
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2"
              placeholder="Enter component description"
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="unit-price" className="block text-sm font-medium text-gray-700 mb-1">
                Unit Price
              </label>
              <input
                id="unit-price"
                type="number"
                step="0.01"
                value={formData.unit_price || ''}
                onChange={(e) => handleChange('unit_price', e.target.value ? Number(e.target.value) : null)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                placeholder="0.00"
              />
            </div>

            <div>
              <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                Currency
              </label>
              <select
                id="currency"
                value={formData.currency || 'USD'}
                onChange={(e) => handleChange('currency', e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CAD">CAD</option>
              </select>
            </div>

            <div>
              <label htmlFor="weight" className="block text-sm font-medium text-gray-700 mb-1">
                Weight (kg)
              </label>
              <input
                id="weight"
                type="number"
                step="0.01"
                value={formData.weight_kg || ''}
                onChange={(e) => handleChange('weight_kg', e.target.value ? Number(e.target.value) : null)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active || false}
                onChange={(e) => handleChange('is_active', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                Active
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_preferred"
                checked={formData.is_preferred || false}
                onChange={(e) => handleChange('is_preferred', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="is_preferred" className="text-sm font-medium text-gray-700">
                Preferred
              </label>
            </div>

            <div>
              <label htmlFor="stock-status" className="block text-sm font-medium text-gray-700 mb-1">
                Stock Status
              </label>
              <select
                id="stock-status"
                value={formData.stock_status || 'available'}
                onChange={(e) => handleChange('stock_status', e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value="available">Available</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
                <option value="discontinued">Discontinued</option>
                <option value="on_order">On Order</option>
              </select>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            
            <Button
              type="submit"
              disabled={isLoading || !isDirty}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {isEditing ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
