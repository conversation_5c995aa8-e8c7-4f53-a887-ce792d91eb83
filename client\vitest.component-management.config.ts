import react from '@vitejs/plugin-react'
import path from 'path'
import { defineConfig } from 'vitest/config'

/**
 * Vitest configuration specifically for Component Management module testing
 * Provides optimized settings for comprehensive testing with high coverage requirements
 */
export default defineConfig({
  plugins: [react()] as any,
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,

    // Include only component management tests
    include: [
      'src/modules/components/**/*.test.{ts,tsx}',
      'src/test/integration/component-management-integration.test.tsx',
    ],

    // Exclude E2E tests (run separately with <PERSON><PERSON>)
    exclude: ['**/e2e/**', '**/node_modules/**', '**/dist/**', '**/.next/**'],

    // Coverage configuration for component management module
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage/component-management',

      // Include only component management files
      include: ['src/modules/components/**/*.{ts,tsx}'],

      // Exclude test files and type definitions
      exclude: [
        'src/modules/components/**/*.test.{ts,tsx}',
        'src/modules/components/**/*.d.ts',
        'src/modules/components/**/index.ts',
        'src/test/**',
        'node_modules/',
        'coverage/',
        'dist/',
        '.next/',
      ],

      // High coverage thresholds for engineering-grade standards
      thresholds: {
        global: {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
        // Per-file thresholds
        'src/modules/components/components/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        'src/modules/components/hooks/': {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
        'src/modules/components/api/': {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
        'src/modules/components/utils.ts': {
          branches: 100,
          functions: 100,
          lines: 100,
          statements: 100,
        },
      },
    },

    // Test timeout settings
    testTimeout: 10000,
    hookTimeout: 10000,

    // Parallel execution for faster testing
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4,
      },
    },

    // Reporter configuration
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/component-management-results.json',
      html: './test-results/component-management-report.html',
    },

    // Mock configuration
    deps: {
      inline: ['@testing-library/react', '@testing-library/jest-dom'],
    },

    // Environment variables for testing
    env: {
      NODE_ENV: 'test',
      VITE_API_BASE_URL: 'http://localhost:8000/api/v1',
    },
  },

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },

  // Optimize for testing
  esbuild: {
    target: 'node14',
  },
})
