/**
 * Re-exports the public API of the 'components' domain module.
 * Includes domain-specific components, hooks, API wrappers, types, and utilities.
 */
export * from './api'; // Re-exports from modules/components/api/index.ts
export * from './hooks'; // Re-exports from modules/components/hooks/index.ts
export * from './types'
export * from './utils'

// Export components with explicit names to avoid conflicts with types
export {
    BulkOperations, ComponentCard, ComponentDetails, ComponentForm, ComponentList
} from './components'

// Export components that conflict with types using aliases
export {
    ComponentFilters as ComponentFiltersWidget, ComponentSearch as ComponentSearchWidget, ComponentStats as ComponentStatsWidget
} from './components'

