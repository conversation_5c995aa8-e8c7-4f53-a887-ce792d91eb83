@import 'tailwindcss';

/* Define custom design tokens using @theme */
@theme {
  --color-brand-primary: 220 90% 50%;
  --color-brand-secondary: 240 50% 50%;
  --color-text-muted: 0 0% 40%;
  --spacing-extra-large: 96px;
}

/* Base styles and CSS variables */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb)))
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Custom component styles using @layer */
@layer components {
  .card-base-style {
    @apply rounded-xl border border-gray-200 bg-white p-6 shadow-lg;
  }

  .btn-primary {
    @apply rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors duration-200 hover:bg-blue-700;
  }

  .btn-secondary {
    @apply rounded-lg bg-gray-200 px-4 py-2 font-medium text-gray-800 transition-colors duration-200 hover:bg-gray-300;
  }

  .hero-gradient {
    @apply bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800;
  }
}
