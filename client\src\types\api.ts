/**
 * TypeScript type definitions for the Ultimate Electrical Designer API
 * Generated based on backend Pydantic schemas
 */

// Base types
export interface BaseSchema {
  id?: number
}

export interface TimestampMixin {
  created_at: string
  updated_at: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// User types
export enum UserRole {
  VIEWER = 'VIEWER',
  EDITOR = 'EDITOR',
  ADMIN = 'ADMIN',
}

export interface UserBase {
  name: string
  email: string
  role: UserRole
  is_active: boolean
}

export interface UserCreate extends UserBase {
  password: string
}

export interface UserUpdate extends Partial<UserBase> {
  password?: string
}

export interface UserRead extends UserBase, TimestampMixin {
  id: number
  is_admin: boolean
  last_login?: string
}

export interface UserSummary {
  id: number
  name: string
  email: string
  role: UserRole
  is_active: boolean
}

export type UserPaginatedResponse = PaginatedResponse<UserRead>

// Authentication types
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: UserRead
}

export interface LogoutResponse {
  message: string
  logged_out_at: string
}

export interface PasswordChangeRequest {
  current_password: string
  new_password: string
}

export interface PasswordChangeResponse {
  message: string
  changed_at: string
}

// Error types
export interface ErrorResponse {
  detail: string
  error_code?: string
  timestamp?: string
}

// API Response wrapper
export interface ApiResponse<T = any> {
  data?: T
  error?: ErrorResponse
  status: number
}

// Query parameters for list endpoints
export interface ListQueryParams {
  skip?: number
  limit?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// Health check types
export interface HealthCheck {
  status: string
  timestamp: string
  version: string
}

// API endpoints configuration
export interface ApiEndpoints {
  auth: {
    login: string
    logout: string
    token: string
    changePassword: string
  }
  users: {
    list: string
    create: string
    read: string
    update: string
    delete: string
    me: string
    summary: string
  }
  components: {
    list: string
    create: string
    read: string
    update: string
    delete: string
    search: string
    advancedSearch: string
    searchBySpecifications: string
    suggestions: string
    preferred: string
    stats: string
    categories: string
    types: string
    bulkCreate: string
    bulkUpdate: string
    bulkDelete: string
    enhancedBulkCreate: string
    selectiveBulkUpdate: string
  }
  health: {
    check: string
  }
}

// API client configuration
export interface ApiClientConfig {
  baseURL: string
  timeout?: number
  headers?: Record<string, string>
}

// Request options
export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  signal?: AbortSignal
}

// Authentication context
export interface AuthContext {
  user: UserRead | null
  isAuthenticated: boolean
  isLoading: boolean
  token: string | null
}

// Admin-specific types
export interface AdminStats {
  total_users: number
  active_users: number
  inactive_users: number
  admin_users: number
}

// Form validation types
export interface ValidationError {
  field: string
  message: string
}

export interface FormErrors {
  [key: string]: string | undefined
}

// React Query keys
export const QueryKeys = {
  users: ['users'] as const,
  usersList: (params?: ListQueryParams) => ['users', 'list', params] as const,
  user: (id: number) => ['users', id] as const,
  currentUser: ['users', 'me'] as const,
  usersSummary: ['users', 'summary'] as const,
  health: ['health'] as const,

  // Component management query keys
  components: ['components'] as const,
  componentsList: (params?: any) => ['components', 'list', params] as const,
  component: (id: number) => ['components', id] as const,
  componentsSearch: (params?: ComponentSearch) => ['components', 'search', params] as const,
  componentsAdvancedSearch: (params?: ComponentAdvancedSearch) =>
    ['components', 'advanced-search', params] as const,
  componentsPreferred: (params?: any) => ['components', 'preferred', params] as const,
  componentsStats: ['components', 'stats'] as const,
  componentsSuggestions: (query: string, field?: string) =>
    ['components', 'suggestions', query, field] as const,
  componentsCategories: ['components', 'categories'] as const,
  componentsTypes: (category?: ComponentCategoryType) => ['components', 'types', category] as const,
} as const

// Component Management Types
export enum ComponentCategoryType {
  POWER_DISTRIBUTION = 'Power Distribution',
  CABLES_WIRING = 'Cables & Wiring',
  PROTECTION_DEVICES = 'Protection Devices',
  SWITCHING_CONTROL = 'Switching & Control',
  MEASUREMENT_MONITORING = 'Measurement & Monitoring',
  ENCLOSURES_MOUNTING = 'Enclosures & Mounting',
  GROUNDING_BONDING = 'Grounding & Bonding',
  POWER_SOURCES = 'Power Sources',
  LOADS = 'Loads',
  COMMUNICATION = 'Communication',
  SAFETY_EMERGENCY = 'Safety & Emergency',
  HEAT_TRACING_SYSTEM = 'Heat Tracing System',
  CABLE_MANAGEMENT = 'Cable Management',
  OTHER_ELECTRICAL = 'Other Electrical',
}

export enum ComponentType {
  SWITCHBOARD = 'Switchboard',
  MOTOR_CONTROL_CENTER = 'Motor Control Center (MCC)',
  DISTRIBUTION_BOARD = 'Distribution Board (DB)',
  PANELBOARD = 'Panelboard',
  MAIN_SWITCHBOARD = 'Main Switchboard (MSB)',
  SUB_SWITCHBOARD = 'Sub Switchboard (SSB)',
  CONTROL_PANEL = 'Control Panel',
  TRANSFORMER = 'Transformer',
  GENERATOR = 'Generator',
  UPS = 'Uninterruptible Power Supply (UPS)',
  BATTERY_BANK = 'Battery Bank',
  DC_POWER_SUPPLY = 'DC Power Supply',
  SPD = 'Surge Protective Device (SPD)',
  CAPACITOR_BANK = 'Capacitor Bank',
  ATS = 'Automatic Transfer Switch (ATS)',
  MTS = 'Manual Transfer Switch (MTS)',
  VFD = 'Variable Frequency Drive (VFD)',
  SOFT_STARTER = 'Soft Starter',
  MOTOR_STARTER = 'Motor Starter',
  CIRCUIT_BREAKER = 'Circuit Breaker',
  FUSE = 'Fuse',
  RCD = 'Residual Current Device (RCD)',
  OVERLOAD_RELAY = 'Overload Relay',
  PROTECTIVE_RELAY = 'Protective Relay',
  POWER_CABLE = 'Power Cable',
  CONTROL_CABLE = 'Control Cable',
  INSTRUMENTATION_CABLE = 'Instrumentation Cable',
  COMMUNICATION_CABLE = 'Communication Cable',
  FIBER_OPTIC_CABLE = 'Fiber Optic Cable',
  COAXIAL_CABLE = 'Coaxial Cable',
  BUSBAR = 'Busbar',
  GROUNDING_CONDUCTOR = 'Grounding Conductor',
  BONDING_CONDUCTOR = 'Bonding Conductor',
  CABLE_TRAY = 'Cable Tray',
  CONDUIT = 'Conduit',
  CABLE_DUCT = 'Cable Duct',
  CABLE_LADDER = 'Cable Ladder',
  DISCONNECT_SWITCH = 'Disconnect Switch',
  LOAD_BREAK_SWITCH = 'Load Break Switch',
  ISOLATION_SWITCH = 'Isolation Switch',
  CONTACTOR = 'Contactor',
  CONTROL_RELAY = 'Control Relay',
  PUSH_BUTTON = 'Push Button',
  SELECTOR_SWITCH = 'Selector Switch',
  PILOT_LIGHT = 'Pilot Light',
  LIMIT_SWITCH = 'Limit Switch',
  PROXIMITY_SWITCH = 'Proximity Switch',
  TIMER = 'Timer',
  COUNTER = 'Counter',
  THERMOSTAT = 'Thermostat',
  PRESSURE_SWITCH = 'Pressure Switch',
  FLOW_SWITCH = 'Flow Switch',
  LEVEL_SWITCH = 'Level Switch',
  AMMETER = 'Ammeter',
  VOLTMETER = 'Voltmeter',
  POWER_METER = 'Power Meter',
  ENERGY_METER = 'Energy Meter',
  CURRENT_TRANSFORMER = 'Current Transformer (CT)',
  VOLTAGE_TRANSFORMER = 'Voltage Transformer (VT/PT)',
  TEMPERATURE_SENSOR = 'Temperature Sensor',
  PRESSURE_SENSOR = 'Pressure Sensor',
  FLOW_SENSOR = 'Flow Sensor',
  LEVEL_SENSOR = 'Level Sensor',
  HUMIDITY_SENSOR = 'Humidity Sensor',
  VIBRATION_SENSOR = 'Vibration Sensor',
  GAS_DETECTOR = 'Gas Detector',
  SMOKE_DETECTOR = 'Smoke Detector',
  MOTION_SENSOR = 'Motion Sensor',
  JUNCTION_BOX = 'Junction Box',
  TERMINAL_BOX = 'Terminal Box',
  ENCLOSURE = 'Enclosure',
  TERMINAL_BLOCK = 'Terminal Block',
  CONNECTOR = 'Connector',
  CABLE_GLAND = 'Cable Gland',
  CABLE_LUG = 'Cable Lug',
  ELECTRIC_MOTOR = 'Electric Motor',
  HEATER = 'Heater',
  LIGHTING_FIXTURE = 'Lighting Fixture',
  FAN = 'Fan',
  PUMP = 'Pump',
  COMPRESSOR = 'Compressor',
  HVAC_UNIT = 'HVAC Unit',
  OVEN = 'Oven',
  FURNACE = 'Furnace',
  WELDING_MACHINE = 'Welding Machine',
  RECTIFIER = 'Rectifier',
  VALVE_ACTUATOR = 'Valve Actuator',
  HEAT_TRACING_CABLE = 'Heat Tracing Cable',
  HEAT_TRACING_CONTROLLER = 'Heat Tracing Controller',
  HEAT_TRACING_POWER_DISTRIBUTION_ENCLOSURE = 'Heat Tracing Power Distribution Enclosure',
  HEAT_TRACING_JUNCTION_BOX = 'Heat Tracing Junction Box',
  EMERGENCY_STOP_BUTTON = 'Emergency Stop Button',
  FIRE_ALARM_CONTROL_PANEL = 'Fire Alarm Control Panel',
  FIRE_ALARM_DETECTOR = 'Fire Alarm Detector',
  STROBE_LIGHT = 'Strobe Light',
  HORN = 'Horn',
  EMERGENCY_LIGHTING_FIXTURE = 'Emergency Lighting Fixture',
  ETHERNET_SWITCH = 'Ethernet Switch',
  WIRELESS_ACCESS_POINT = 'Wireless Access Point',
  FIBER_OPTIC_TRANSCEIVER = 'Fiber Optic Transceiver',
  INDUSTRIAL_ROUTER = 'Industrial Router',
  JUNCTION_TERMINAL = 'Junction Terminal',
  MISCELLANEOUS_ELECTRICAL_COMPONENT = 'Miscellaneous Electrical Component',
}

// Component dimensions interface
export interface ComponentDimensions {
  length?: number | null
  width?: number | null
  height?: number | null
  diameter?: number | null
  unit: string
}

// Component specifications interface
export interface ComponentSpecifications {
  electrical?: Record<string, any>
  mechanical?: Record<string, any>
  environmental?: Record<string, any>
  [key: string]: any
}

// Component base interface
export interface ComponentBase {
  name: string
  manufacturer: string
  model_number: string
  description?: string | null
  component_type?: ComponentType | null
  category?: ComponentCategoryType | null
  component_type_id?: number | null
  component_category_id?: number | null
  specifications?: ComponentSpecifications | Record<string, any> | null
  unit_price?: number | string | null
  currency: string
  supplier?: string | null
  part_number?: string | null
  weight_kg?: number | null
  dimensions?: ComponentDimensions | null
  is_active: boolean
  is_preferred: boolean
  stock_status: string
  version: string
  metadata?: Record<string, any> | null
}

// Component create interface
export interface ComponentCreate extends ComponentBase {}

// Component update interface
export interface ComponentUpdate extends Partial<ComponentBase> {}

// Component read interface
export interface ComponentRead extends ComponentBase, TimestampMixin {
  id: number
  full_name?: string | null
  display_name?: string | null
}

// Component summary interface
export interface ComponentSummary {
  id: number
  name: string
  manufacturer: string
  model_number: string
  component_type?: ComponentType | null
  category?: ComponentCategoryType | null
  unit_price?: string | null
  currency: string
  is_preferred: boolean
  is_active: boolean
}

// Component search interfaces
export interface ComponentSearch {
  search_term?: string | null
  category?: ComponentCategoryType | null
  component_type?: ComponentType | null
  manufacturer?: string | null
  is_preferred?: boolean | null
  is_active?: boolean | null
  min_price?: number | string | null
  max_price?: number | string | null
  currency?: string | null
  stock_status?: string | null
  specifications?: Record<string, any> | null
}

// Advanced search filter interfaces
export interface AdvancedFilter {
  field: string
  operator: string
  value: any
  logical_operator?: 'and' | 'or'
}

export interface SpecificationFilter {
  path: string
  operator: string
  value: any
  data_type: string
  unit?: string | null
  logical_operator?: 'and' | 'or'
}

export interface RangeFilter {
  field: string
  min_value?: any
  max_value?: any
  unit?: string | null
  logical_operator?: 'and' | 'or'
}

export interface ComponentAdvancedSearch {
  search_term?: string | null
  basic_filters?: AdvancedFilter[] | null
  specification_filters?: SpecificationFilter[] | null
  range_filters?: RangeFilter[] | null
  sort_by?: string | null
  sort_order?: 'asc' | 'desc' | null
  include_inactive?: boolean
  include_deleted?: boolean
}

// Component search result interface
export interface ComponentSearchResult {
  component: ComponentRead
  relevance_score?: number | null
  match_highlights?: string[] | null
}

// Paginated response for components
export type ComponentPaginatedResponse = PaginatedResponse<ComponentRead>
export type ComponentAdvancedSearchResponse = {
  items: ComponentSearchResult[]
  pagination: {
    page: number
    size: number
    total: number
    pages: number
  }
  search_metadata?: Record<string, any> | null
}

// Bulk operation interfaces
export interface ComponentBulkCreate {
  components: ComponentCreate[]
  validate_duplicates?: boolean
  skip_validation?: boolean
}

export interface ComponentBulkUpdate {
  component_ids: number[]
  update_data: ComponentUpdate
  skip_missing?: boolean
}

export interface ComponentValidationResult {
  is_valid: boolean
  component?: ComponentRead | null
  errors?: string[] | null
  warnings?: string[] | null
}

// Component statistics interface
export interface ComponentStats {
  total_components: number
  active_components: number
  inactive_components: number
  preferred_components: number
  by_category: Record<string, number>
  by_type: Record<string, number>
  by_manufacturer: Record<string, number>
  price_range: {
    min: number | null
    max: number | null
    average: number | null
  }
}

// Mutation keys
export const MutationKeys = {
  login: ['auth', 'login'] as const,
  logout: ['auth', 'logout'] as const,
  changePassword: ['auth', 'changePassword'] as const,
  createUser: ['users', 'create'] as const,
  updateUser: ['users', 'update'] as const,
  deleteUser: ['users', 'delete'] as const,
  updateProfile: ['users', 'updateProfile'] as const,

  // Component management mutation keys
  createComponent: ['components', 'create'] as const,
  updateComponent: ['components', 'update'] as const,
  deleteComponent: ['components', 'delete'] as const,
  bulkCreateComponents: ['components', 'bulk-create'] as const,
  bulkUpdateComponents: ['components', 'bulk-update'] as const,
  bulkDeleteComponents: ['components', 'bulk-delete'] as const,
} as const
